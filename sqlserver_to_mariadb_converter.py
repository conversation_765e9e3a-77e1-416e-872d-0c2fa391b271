#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Server to MariaDB Converter
将SQL Server导出的备份SQL转换为MariaDB兼容的SQL格式
"""

import re
import os
import sys
import argparse
from typing import List, Tuple


class SQLServerToMariaDBConverter:
    def __init__(self):
        # 数据类型映射
        self.data_type_mappings = {
            # 字符串类型 - 注意顺序很重要，先处理带括号的
            r'\[nvarchar\]\s*\(\s*(\d+)\s*\)': r'VARCHAR(\1)',
            r'\[nvarchar\]\s*\(\s*MAX\s*\)': 'LONGTEXT',
            r'\bnvarchar\s*\(\s*(\d+)\s*\)': r'VARCHAR(\1)',
            r'\bnvarchar\s*\(\s*MAX\s*\)': 'LONGTEXT',
            r'\bNVARCHAR\s*\(\s*(\d+)\s*\)': r'VARCHAR(\1)',
            r'\bNVARCHAR\s*\(\s*MAX\s*\)': 'LONGTEXT',
            r'\bNTEXT\b': 'LONGTEXT',
            r'\bTEXT\b': 'TEXT',
            r'\bNCHAR\s*\(\s*(\d+)\s*\)': r'CHAR(\1)',
            
            # 数值类型 - 处理带方括号和不带方括号的情况
            r'\[bit\]': 'TINYINT(1)',
            r'\[int\]': 'INT',
            r'\[tinyint\]': 'TINYINT',
            r'\[smallint\]': 'SMALLINT',
            r'\[bigint\]': 'BIGINT',
            r'\bBIT\b': 'TINYINT(1)',
            r'\bTINYINT\b': 'TINYINT',
            r'\bSMALLINT\b': 'SMALLINT',
            r'\bINT\b': 'INT',
            r'\bBIGINT\b': 'BIGINT',
            r'\bDECIMAL\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)': r'DECIMAL(\1,\2)',
            r'\bNUMERIC\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)': r'DECIMAL(\1,\2)',
            r'\bFLOAT\b': 'DOUBLE',
            r'\bREAL\b': 'FLOAT',
            r'\bMONEY\b': 'DECIMAL(19,4)',
            r'\bSMALLMONEY\b': 'DECIMAL(10,4)',
            
            # 日期时间类型 - 处理带方括号和精度的情况
            r'\[datetime2\]\s*\(\s*\d+\s*\)': 'DATETIME',
            r'\[datetime2\]': 'DATETIME',
            r'\[datetime\]': 'DATETIME',
            r'\[smalldatetime\]': 'DATETIME',
            r'\[date\]': 'DATE',
            r'\[time\]': 'TIME',
            r'\bDATETIME2\s*\(\s*\d+\s*\)': 'DATETIME',
            r'\bDATETIME2\b': 'DATETIME',
            r'\bDATETIME\b': 'DATETIME',
            r'\bSMALLDATETIME\b': 'DATETIME',
            r'\bDATE\b': 'DATE',
            r'\bTIME\b': 'TIME',
            r'\bTIMESTAMP\b': 'TIMESTAMP',
            
            # 二进制类型
            r'\bUNIQUEIDENTIFIER\b': 'VARCHAR(36)',
            r'\bIMAGE\b': 'LONGBLOB',
            r'\bVARBINARY\s*\(\s*MAX\s*\)': 'LONGBLOB',
            r'\bVARBINARY\s*\(\s*(\d+)\s*\)': r'VARBINARY(\1)',
            r'\bBINARY\s*\(\s*(\d+)\s*\)': r'BINARY(\1)',
        }
        
        # 函数映射
        self.function_mappings = {
            r'\bGETDATE\s*\(\s*\)': 'NOW()',
            r'\bNEWID\s*\(\s*\)': 'UUID()',
            r'\bLEN\s*\(': 'LENGTH(',
            r'\bISNULL\s*\(': 'IFNULL(',
            r'\bDATEDIFF\s*\(\s*day\s*,': 'DATEDIFF(',
        }

    def convert_identifiers(self, sql: str) -> str:
        """转换标识符：方括号 → 反引号"""
        # 替换 [identifier] 为 `identifier`
        sql = re.sub(r'\[([^\]]+)\]', r'`\1`', sql)
        return sql

    def convert_data_types(self, sql: str) -> str:
        """转换数据类型"""
        for pattern, replacement in self.data_type_mappings.items():
            sql = re.sub(pattern, replacement, sql, flags=re.IGNORECASE)
        return sql

    def convert_functions(self, sql: str) -> str:
        """转换函数"""
        for pattern, replacement in self.function_mappings.items():
            sql = re.sub(pattern, replacement, sql, flags=re.IGNORECASE)
        return sql

    def handle_identity_columns(self, sql: str) -> str:
        """处理IDENTITY列"""
        # 转换 IDENTITY(1,1) 为 AUTO_INCREMENT
        sql = re.sub(r'\bIDENTITY\s*\(\s*\d+\s*,\s*\d+\s*\)', 'AUTO_INCREMENT', sql, flags=re.IGNORECASE)
        
        # 移除 SET IDENTITY_INSERT 语句
        sql = re.sub(r'SET\s+IDENTITY_INSERT\s+.*?\s+(ON|OFF)\s*;?\s*', '', sql, flags=re.IGNORECASE | re.MULTILINE)
        
        return sql

    def handle_go_statements(self, sql: str) -> str:
        """处理GO语句分隔符"""
        # 移除单独的GO语句
        sql = re.sub(r'^\s*GO\s*$', '', sql, flags=re.MULTILINE | re.IGNORECASE)
        return sql

    def handle_use_database(self, sql: str) -> str:
        """处理USE数据库语句"""
        # 转换 USE [database] 为 USE `database`
        sql = re.sub(r'USE\s+\[([^\]]+)\]', r'USE `\1`', sql, flags=re.IGNORECASE)
        return sql

    def remove_sqlserver_specific(self, sql: str) -> str:
        """移除SQL Server特有的语句"""
        patterns_to_remove = [
            r'SET\s+ANSI_NULLS\s+(ON|OFF)\s*;?\s*',
            r'SET\s+QUOTED_IDENTIFIER\s+(ON|OFF)\s*;?\s*',
            r'SET\s+ANSI_PADDING\s+(ON|OFF)\s*;?\s*',
            r'SET\s+ANSI_WARNINGS\s+(ON|OFF)\s*;?\s*',
            r'SET\s+ARITHABORT\s+(ON|OFF)\s*;?\s*',
            r'SET\s+CONCAT_NULL_YIELDS_NULL\s+(ON|OFF)\s*;?\s*',
            r'SET\s+NUMERIC_ROUNDABORT\s+(ON|OFF)\s*;?\s*',
            r'SET\s+XACT_ABORT\s+(ON|OFF)\s*;?\s*',
            r'IF\s+NOT\s+EXISTS\s*\([^)]*\)\s*',
            r'WITH\s+CHECK\s+ADD\s+CONSTRAINT\s+',
            r'ALTER\s+TABLE\s+.*?\s+CHECK\s+CONSTRAINT\s+.*?;?\s*',
        ]
        
        for pattern in patterns_to_remove:
            sql = re.sub(pattern, '', sql, flags=re.IGNORECASE | re.MULTILINE)
        
        return sql

    def handle_constraints(self, sql: str) -> str:
        """处理约束语法"""
        # 转换主键约束 - 移除CLUSTERED关键字
        sql = re.sub(r'CONSTRAINT\s+`([^`]+)`\s+PRIMARY\s+KEY\s+CLUSTERED\s*\(([^)]+)\)',
                    r'PRIMARY KEY (\2)', sql, flags=re.IGNORECASE)

        # 转换外键约束
        sql = re.sub(r'CONSTRAINT\s+`([^`]+)`\s+FOREIGN\s+KEY\s*\(([^)]+)\)\s+REFERENCES\s+([^\s(]+)\s*\(([^)]+)\)',
                    r'FOREIGN KEY (\2) REFERENCES \3(\4)', sql, flags=re.IGNORECASE)

        # 移除CLUSTERED和NONCLUSTERED关键字
        sql = re.sub(r'\s+CLUSTERED\b', '', sql, flags=re.IGNORECASE)
        sql = re.sub(r'\s+NONCLUSTERED\b', '', sql, flags=re.IGNORECASE)

        return sql

    def clean_mariadb_syntax(self, sql: str) -> str:
        """清理MariaDB不支持的语法"""
        # 移除精度参数从DATETIME
        sql = re.sub(r'DATETIME\s*\(\s*\d+\s*\)', 'DATETIME', sql, flags=re.IGNORECASE)

        # 处理反引号内的数据类型（这些不应该被反引号包围）
        data_types = ['INT', 'VARCHAR', 'CHAR', 'TEXT', 'LONGTEXT', 'TINYINT', 'SMALLINT', 'BIGINT',
                     'DECIMAL', 'FLOAT', 'DOUBLE', 'DATETIME', 'DATE', 'TIME', 'TIMESTAMP',
                     'LONGBLOB', 'VARBINARY', 'BINARY', 'nvarchar']

        for dtype in data_types:
            # 移除数据类型周围的反引号
            sql = re.sub(rf'`{dtype}`(\s*\([^)]*\))?', rf'{dtype}\1', sql, flags=re.IGNORECASE)

        # 处理带参数的数据类型
        sql = re.sub(r'`(DECIMAL|VARCHAR|CHAR|VARBINARY|BINARY)\s*\(([^)]+)\)`', r'\1(\2)', sql, flags=re.IGNORECASE)

        # 清理多余的括号
        sql = re.sub(r'DEFAULT\s+\(\s*\(([^)]+)\)\s*\)', r'DEFAULT (\1)', sql, flags=re.IGNORECASE)

        return sql

    def clean_whitespace(self, sql: str) -> str:
        """清理多余的空白字符"""
        # 移除多余的空行
        sql = re.sub(r'\n\s*\n\s*\n', '\n\n', sql)
        # 移除行尾空格
        sql = re.sub(r'[ \t]+$', '', sql, flags=re.MULTILINE)
        return sql.strip()

    def convert_sql(self, sql_content: str) -> str:
        """执行完整的SQL转换"""
        print("开始转换SQL...")
        
        # 1. 转换标识符
        sql_content = self.convert_identifiers(sql_content)
        print("✓ 标识符转换完成")
        
        # 2. 转换数据类型
        sql_content = self.convert_data_types(sql_content)
        print("✓ 数据类型转换完成")
        
        # 3. 转换函数
        sql_content = self.convert_functions(sql_content)
        print("✓ 函数转换完成")
        
        # 4. 处理IDENTITY列
        sql_content = self.handle_identity_columns(sql_content)
        print("✓ IDENTITY列处理完成")
        
        # 5. 处理GO语句
        sql_content = self.handle_go_statements(sql_content)
        print("✓ GO语句处理完成")
        
        # 6. 处理USE数据库语句
        sql_content = self.handle_use_database(sql_content)
        print("✓ USE语句处理完成")
        
        # 7. 移除SQL Server特有语句
        sql_content = self.remove_sqlserver_specific(sql_content)
        print("✓ SQL Server特有语句移除完成")
        
        # 8. 处理约束
        sql_content = self.handle_constraints(sql_content)
        print("✓ 约束处理完成")

        # 9. 清理MariaDB语法
        sql_content = self.clean_mariadb_syntax(sql_content)
        print("✓ MariaDB语法清理完成")

        # 10. 清理空白字符
        sql_content = self.clean_whitespace(sql_content)
        print("✓ 空白字符清理完成")
        
        return sql_content

    def convert_file(self, input_file: str, output_file: str = None) -> str:
        """转换SQL文件"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 确定输出文件名
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_mariadb.sql"
        
        print(f"读取文件: {input_file}")
        
        # 读取输入文件
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            with open(input_file, 'r', encoding='gbk') as f:
                sql_content = f.read()
        
        # 执行转换
        converted_sql = self.convert_sql(sql_content)
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(converted_sql)
        
        print(f"转换完成! 输出文件: {output_file}")
        return output_file


def main():
    parser = argparse.ArgumentParser(description='将SQL Server备份SQL转换为MariaDB兼容格式')
    parser.add_argument('input_file', help='输入的SQL Server SQL文件')
    parser.add_argument('-o', '--output', help='输出文件名 (可选)')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    try:
        converter = SQLServerToMariaDBConverter()
        output_file = converter.convert_file(args.input_file, args.output)
        
        print(f"\n转换成功!")
        print(f"输入文件: {args.input_file}")
        print(f"输出文件: {output_file}")
        print(f"\n请在导入MariaDB前检查转换结果，并根据需要进行调整。")
        
    except Exception as e:
        print(f"转换失败: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
