#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从INSERT语句生成CREATE TABLE语句
分析数据类型并创建表结构
"""

import re
import os

def analyze_insert_statements(sql_file):
    """分析INSERT语句，提取表结构信息"""
    
    print(f"分析文件: {sql_file}")
    
    # 读取文件
    with open(sql_file, 'r', encoding='utf-8') as f:
        content = f.read(100000)  # 读取前100KB进行分析
    
    # 查找所有INSERT语句
    insert_pattern = r'INSERT INTO `?([^`\s]+)`?\s*\(([^)]+)\)\s*VALUES'
    matches = re.findall(insert_pattern, content, re.IGNORECASE)
    
    if not matches:
        print("未找到INSERT语句")
        return {}
    
    tables = {}
    
    for table_name, columns_str in matches:
        if table_name not in tables:
            # 清理列名
            columns = [col.strip().strip('[]`') for col in columns_str.split(',')]
            tables[table_name] = columns
            print(f"发现表: {table_name} ({len(columns)} 列)")
    
    return tables

def analyze_data_types(sql_file, table_name, columns):
    """分析数据类型"""
    
    print(f"分析 {table_name} 表的数据类型...")
    
    # 读取更多内容来分析数据类型
    with open(sql_file, 'r', encoding='utf-8') as f:
        # 查找该表的VALUES部分
        content = f.read(500000)  # 读取前500KB
    
    # 查找VALUES数据
    values_pattern = rf'INSERT INTO `?{re.escape(table_name)}`?[^)]+\)\s*VALUES\s*\((.*?)\)'
    values_matches = re.findall(values_pattern, content, re.IGNORECASE | re.DOTALL)
    
    if not values_matches:
        print(f"未找到 {table_name} 的VALUES数据")
        return None
    
    # 分析第一行数据来推断类型
    first_row = values_matches[0]
    values = []
    
    # 简单的值分割（处理引号内的逗号）
    in_quotes = False
    current_value = ""
    quote_char = None
    
    for char in first_row:
        if char in ["'", '"', 'N'] and not in_quotes:
            if char == 'N' and current_value == "":
                current_value = char
                continue
            quote_char = char if char != 'N' else "'"
            in_quotes = True
            current_value += char
        elif char == quote_char and in_quotes:
            in_quotes = False
            current_value += char
        elif char == ',' and not in_quotes:
            values.append(current_value.strip())
            current_value = ""
        else:
            current_value += char
    
    if current_value.strip():
        values.append(current_value.strip())
    
    # 推断数据类型
    column_types = []
    for i, (col_name, value) in enumerate(zip(columns, values)):
        if i >= len(values):
            break
            
        value = value.strip()
        
        # 推断类型
        if value.upper() in ['NULL', '']:
            col_type = 'VARCHAR(255)'
        elif value.startswith("N'") or value.startswith("'"):
            # 字符串类型
            content_length = len(value) - 3 if value.startswith("N'") else len(value) - 2
            if content_length <= 50:
                col_type = 'VARCHAR(100)'
            elif content_length <= 255:
                col_type = 'VARCHAR(255)'
            else:
                col_type = 'TEXT'
        elif '.' in value and value.replace('.', '').replace('-', '').isdigit():
            # 浮点数
            col_type = 'DECIMAL(18,2)'
        elif value.replace('-', '').isdigit():
            # 整数
            num = int(value)
            if -128 <= num <= 127:
                col_type = 'TINYINT'
            elif -32768 <= num <= 32767:
                col_type = 'SMALLINT'
            elif -2147483648 <= num <= 2147483647:
                col_type = 'INT'
            else:
                col_type = 'BIGINT'
        elif 'date' in col_name.lower() or 'time' in col_name.lower():
            col_type = 'DATETIME'
        else:
            col_type = 'VARCHAR(255)'
        
        column_types.append((col_name, col_type))
    
    return column_types

def generate_create_table(table_name, column_types):
    """生成CREATE TABLE语句"""
    
    sql = f"CREATE TABLE `{table_name}` (\n"
    
    for i, (col_name, col_type) in enumerate(column_types):
        comma = "," if i < len(column_types) - 1 else ""
        sql += f"    `{col_name}` {col_type}{comma}\n"
    
    sql += ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n"
    
    return sql

def main():
    """主函数"""
    sql_file = "export_202511111009_clean.sql"
    
    if not os.path.exists(sql_file):
        print(f"文件不存在: {sql_file}")
        return
    
    print("=" * 60)
    print("生成表结构")
    print("=" * 60)
    
    # 分析INSERT语句
    tables = analyze_insert_statements(sql_file)
    
    if not tables:
        print("未找到表信息")
        return
    
    # 生成CREATE TABLE语句
    create_statements = []
    
    for table_name, columns in tables.items():
        print(f"\n处理表: {table_name}")
        
        # 分析数据类型
        column_types = analyze_data_types(sql_file, table_name, columns)
        
        if column_types:
            create_sql = generate_create_table(table_name, column_types)
            create_statements.append(create_sql)
            print(f"✓ 生成了 {table_name} 的CREATE语句")
        else:
            # 如果无法分析数据类型，使用默认类型
            default_types = [(col, 'VARCHAR(255)') for col in columns]
            create_sql = generate_create_table(table_name, default_types)
            create_statements.append(create_sql)
            print(f"✓ 使用默认类型生成 {table_name} 的CREATE语句")
    
    # 保存CREATE语句
    output_file = "create_tables.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 自动生成的表结构\n")
        f.write("-- 请在导入数据前先执行这些CREATE TABLE语句\n\n")
        for create_sql in create_statements:
            f.write(create_sql)
    
    print(f"\n" + "=" * 60)
    print("生成完成!")
    print(f"CREATE语句已保存到: {output_file}")
    print(f"共生成 {len(create_statements)} 个表的结构")
    print("\n导入顺序:")
    print(f"1. 先执行: mysql -u root -p database_name < {output_file}")
    print(f"2. 再执行: mysql -u root -p database_name < {sql_file}")
    print("=" * 60)

if __name__ == '__main__':
    main()
