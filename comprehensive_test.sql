
USE [CompanyDB]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- 用户表
-- =============================================
CREATE TABLE [dbo].[Users](
    [UserID] [int] IDENTITY(1,1) NOT NULL,
    [Username] [nvarchar](50) NOT NULL,
    [Email] [nvarchar](100) NOT NULL,
    [Password] [nvarchar](255) NOT NULL,
    [FirstName] [nvarchar](50) NULL,
    [LastName] [nvarchar](50) NULL,
    [IsActive] [bit] NOT NULL DEFAULT ((1)),
    [IsAdmin] [bit] NOT NULL DEFAULT ((0)),
    [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
    [ModifiedDate] [datetime2](7) NULL,
    [LastLoginDate] [datetime] NULL,
    [UserGuid] [uniqueidentifier] NOT NULL DEFAULT (newid()),
    [ProfileImage] [image] NULL,
    [Balance] [money] NOT NULL DEFAULT ((0.00)),
    [Notes] [ntext] NULL,
    [Age] [tinyint] NULL,
    [Salary] [decimal](10,2) NULL,
    [Rating] [float] NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([UserID] ASC)
)
GO

-- =============================================
-- 部门表
-- =============================================
CREATE TABLE [dbo].[Departments](
    [DepartmentID] [int] IDENTITY(1,1) NOT NULL,
    [DepartmentName] [nvarchar](100) NOT NULL,
    [Description] [nvarchar](500) NULL,
    [ManagerID] [int] NULL,
    [Budget] [money] NOT NULL DEFAULT ((0)),
    [IsActive] [bit] NOT NULL DEFAULT ((1)),
    [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
    CONSTRAINT [PK_Departments] PRIMARY KEY CLUSTERED ([DepartmentID] ASC)
)
GO

-- =============================================
-- 员工表
-- =============================================
CREATE TABLE [dbo].[Employees](
    [EmployeeID] [int] IDENTITY(1,1) NOT NULL,
    [UserID] [int] NOT NULL,
    [DepartmentID] [int] NOT NULL,
    [EmployeeNumber] [nvarchar](20) NOT NULL,
    [Position] [nvarchar](100) NOT NULL,
    [HireDate] [date] NOT NULL,
    [Salary] [money] NOT NULL,
    [IsFullTime] [bit] NOT NULL DEFAULT ((1)),
    [WorkHours] [decimal](5,2) NOT NULL DEFAULT ((40.00)),
    [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
    CONSTRAINT [PK_Employees] PRIMARY KEY CLUSTERED ([EmployeeID] ASC)
)
GO

-- =============================================
-- 添加外键约束
-- =============================================
ALTER TABLE [dbo].[Departments]
ADD CONSTRAINT [FK_Departments_Users] FOREIGN KEY([ManagerID])
REFERENCES [dbo].[Users] ([UserID])
GO

ALTER TABLE [dbo].[Employees]
ADD CONSTRAINT [FK_Employees_Users] FOREIGN KEY([UserID])
REFERENCES [dbo].[Users] ([UserID])
GO

ALTER TABLE [dbo].[Employees]
ADD CONSTRAINT [FK_Employees_Departments] FOREIGN KEY([DepartmentID])
REFERENCES [dbo].[Departments] ([DepartmentID])
GO

-- =============================================
-- 插入测试数据
-- =============================================
SET IDENTITY_INSERT [dbo].[Users] ON
GO

INSERT INTO [dbo].[Users] ([UserID], [Username], [Email], [Password], [FirstName], [LastName], [IsAdmin])
VALUES 
    (1, N'admin', N'<EMAIL>', N'admin123', N'系统', N'管理员', 1),
    (2, N'john.doe', N'<EMAIL>', N'password123', N'John', N'Doe', 0),
    (3, N'jane.smith', N'<EMAIL>', N'password456', N'Jane', N'Smith', 0),
    (4, N'bob.wilson', N'<EMAIL>', N'password789', N'Bob', N'Wilson', 0)
GO

SET IDENTITY_INSERT [dbo].[Users] OFF
GO

INSERT INTO [dbo].[Departments] ([DepartmentName], [Description], [ManagerID], [Budget])
VALUES 
    (N'信息技术部', N'负责公司IT基础设施和软件开发', 2, 500000.00),
    (N'人力资源部', N'负责员工招聘、培训和管理', 3, 200000.00),
    (N'财务部', N'负责公司财务管理和会计工作', 4, 150000.00)
GO

INSERT INTO [dbo].[Employees] ([UserID], [DepartmentID], [EmployeeNumber], [Position], [HireDate], [Salary])
VALUES 
    (2, 1, N'EMP001', N'高级软件工程师', '2023-01-15', 8000.00),
    (3, 2, N'EMP002', N'人力资源经理', '2023-02-01', 7000.00),
    (4, 3, N'EMP003', N'财务分析师', '2023-03-01', 6500.00)
GO

-- =============================================
-- 创建视图
-- =============================================
CREATE VIEW [dbo].[EmployeeDetails] AS
SELECT 
    e.[EmployeeID],
    e.[EmployeeNumber],
    u.[FirstName] + ' ' + u.[LastName] AS [FullName],
    u.[Email],
    d.[DepartmentName],
    e.[Position],
    e.[HireDate],
    e.[Salary],
    CASE WHEN e.[IsFullTime] = 1 THEN N'全职' ELSE N'兼职' END AS [EmploymentType],
    DATEDIFF(day, e.[HireDate], GETDATE()) AS [DaysEmployed]
FROM [dbo].[Employees] e
INNER JOIN [dbo].[Users] u ON e.[UserID] = u.[UserID]
INNER JOIN [dbo].[Departments] d ON e.[DepartmentID] = d.[DepartmentID]
WHERE u.[IsActive] = 1
GO

-- =============================================
-- 创建存储过程
-- =============================================
CREATE PROCEDURE [dbo].[GetEmployeesByDepartment]
    @DepartmentID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        e.[EmployeeID],
        e.[EmployeeNumber],
        u.[FirstName],
        u.[LastName],
        u.[Email],
        e.[Position],
        e.[Salary]
    FROM [dbo].[Employees] e
    INNER JOIN [dbo].[Users] u ON e.[UserID] = u.[UserID]
    WHERE e.[DepartmentID] = @DepartmentID
        AND u.[IsActive] = 1
    ORDER BY u.[LastName], u.[FirstName]
END
GO

-- =============================================
-- 创建函数
-- =============================================
CREATE FUNCTION [dbo].[GetEmployeeCount](@DepartmentID INT)
RETURNS INT
AS
BEGIN
    DECLARE @Count INT
    
    SELECT @Count = COUNT(*)
    FROM [dbo].[Employees] e
    INNER JOIN [dbo].[Users] u ON e.[UserID] = u.[UserID]
    WHERE e.[DepartmentID] = @DepartmentID
        AND u.[IsActive] = 1
    
    RETURN ISNULL(@Count, 0)
END
GO
