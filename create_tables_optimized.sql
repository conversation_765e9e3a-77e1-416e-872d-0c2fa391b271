-- 优化的表结构
-- 针对xmMeters表的字段类型进行了优化

CREATE TABLE `xmMeters` (
    `fdno` VARCHAR(20) COMMENT '房栋号',
    `pcno` VARCHAR(20) COMMENT '批次号',
    `jfDate` DATETIME COMMENT '缴费日期',
    `flno` VARCHAR(20) COMMENT '楼层号',
    `wyno` VARCHAR(20) COMMENT '物业号',
    `yhno` VARCHAR(50) COMMENT '用户号',
    `xmno` VARCHAR(50) COMMENT '项目号',
    `syds` DECIMAL(18,2) DEFAULT 0 COMMENT '上月读数',
    `byds` DECIMAL(18,2) DEFAULT 0 COMMENT '本月读数',
    `bb` DECIMAL(18,2) DEFAULT 0 COMMENT '倍率',
    `sjyl` DECIMAL(18,2) DEFAULT 0 COMMENT '实际用量',
    `dj` DECIMAL(18,2) DEFAULT 0 COMMENT '单价',
    `sjje` DECIMAL(18,2) DEFAULT 0 COMMENT '实际金额',
    `shsl` DECIMAL(18,2) DEFAULT 0 COMMENT '审核数量',
    `shje` DECIMAL(18,2) DEFAULT 0 COMMENT '审核金额',
    `je` DECIMAL(18,2) DEFAULT 0 COMMENT '金额',
    `fromDate` DATETIME COMMENT '开始日期',
    `toDate` DATETIME COMMENT '结束日期',
    `jsDate` DATETIME COMMENT '结算日期',
    `bz` VARCHAR(255) COMMENT '备注',
    `opname` VARCHAR(50) COMMENT '操作员',
    `sh` TINYINT(1) DEFAULT 0 COMMENT '审核状态',
    `shname` VARCHAR(50) COMMENT '审核人',
    `cbdj` DECIMAL(18,2) DEFAULT 0 COMMENT '成本单价',
    `fj1` DECIMAL(18,2) DEFAULT 0 COMMENT '附加1',
    `fj2` DECIMAL(18,2) DEFAULT 0 COMMENT '附加2',
    `fj3` DECIMAL(18,2) DEFAULT 0 COMMENT '附加3',
    `psjyl` DECIMAL(18,2) DEFAULT 0 COMMENT '平时用量',
    `pdj` DECIMAL(18,2) DEFAULT 0 COMMENT '平时单价',
    `pje` DECIMAL(18,2) DEFAULT 0 COMMENT '平时金额',
    `fsjyl` DECIMAL(18,2) DEFAULT 0 COMMENT '峰时用量',
    `fdj` DECIMAL(18,2) DEFAULT 0 COMMENT '峰时单价',
    `fje` DECIMAL(18,2) DEFAULT 0 COMMENT '峰时金额',
    `gsjyl` DECIMAL(18,2) DEFAULT 0 COMMENT '谷时用量',
    `gdj` DECIMAL(18,2) DEFAULT 0 COMMENT '谷时单价',
    `gje` DECIMAL(18,2) DEFAULT 0 COMMENT '谷时金额',
    `xb` DECIMAL(18,2) DEFAULT 0 COMMENT '系数',
    `maxds` DECIMAL(18,2) DEFAULT 0 COMMENT '最大读数',
    `meterName` VARCHAR(100) COMMENT '表名',
    `minds` DECIMAL(18,2) DEFAULT 0 COMMENT '最小读数',
    `meterName1` VARCHAR(100) COMMENT '表名1',
    `devName` VARCHAR(100) COMMENT '设备名',
    `parMeter` VARCHAR(100) COMMENT '父表',
    `meterAttr` VARCHAR(100) COMMENT '表属性',
    `addXm` VARCHAR(100) COMMENT '增加项目',
    `decXm` VARCHAR(100) COMMENT '减少项目',
    `Gtxmname` VARCHAR(100) COMMENT 'GT项目名',
    `meterIndex` DECIMAL(18,2) DEFAULT 0 COMMENT '表索引',
    `zfsjyl` DECIMAL(18,2) DEFAULT 0 COMMENT '作废实际用量',
    `zfdj` DECIMAL(18,2) DEFAULT 0 COMMENT '作废单价',
    `zfje` DECIMAL(18,2) DEFAULT 0 COMMENT '作废金额',
    `UseElectricity` VARCHAR(50) COMMENT '用电量',
    `RemainingAmount` VARCHAR(50) COMMENT '剩余金额',
    `MeterStatus` VARCHAR(50) COMMENT '表状态',
    `MeterState` VARCHAR(50) COMMENT '表状态2',
    `MeterReadTime` DATETIME COMMENT '抄表时间',
    `MeterCreateDate` DATETIME COMMENT '表创建日期',
    `tzdType` VARCHAR(50) COMMENT '通知单类型',
    `batch` VARCHAR(50) COMMENT '批次',
    `fsjyl_t` DECIMAL(18,2) DEFAULT 0 COMMENT '峰时用量_t',
    `zfsjyl_t` DECIMAL(18,2) DEFAULT 0 COMMENT '作废实际用量_t',
    `psjyl_t` DECIMAL(18,2) DEFAULT 0 COMMENT '平时用量_t',
    `gsjyl_t` DECIMAL(18,2) DEFAULT 0 COMMENT '谷时用量_t',
    `fpgSum` DECIMAL(18,2) DEFAULT 0 COMMENT 'FPG总和',
    `meterTime` DATETIME COMMENT '表时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电表数据表';

-- 创建索引以提高查询性能
CREATE INDEX idx_fdno ON `xmMeters`(`fdno`);
CREATE INDEX idx_wyno ON `xmMeters`(`wyno`);
CREATE INDEX idx_jfdate ON `xmMeters`(`jfDate`);
CREATE INDEX idx_yhno ON `xmMeters`(`yhno`);
CREATE INDEX idx_pcno ON `xmMeters`(`pcno`);
