#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Server to MariaDB Converter - 使用示例
"""

from sqlserver_to_mariadb_converter import SQLServerToMariaDBConverter


def example_string_conversion():
    """示例：转换SQL字符串"""
    print("=== 字符串转换示例 ===")
    
    # 创建转换器
    converter = SQLServerToMariaDBConverter()
    
    # 示例SQL Server语句
    sqlserver_sql = """
    USE [MyDatabase]
    GO
    
    SET ANSI_NULLS ON
    GO
    
    CREATE TABLE [dbo].[Users](
        [ID] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](50) NOT NULL,
        [Email] [nvarchar](100) NULL,
        [IsActive] [bit] NOT NULL DEFAULT ((1)),
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
        [UserGuid] [uniqueidentifier] NOT NULL DEFAULT (newid()),
        [ProfileImage] [image] NULL,
        [Balance] [money] NOT NULL DEFAULT ((0)),
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([ID] ASC)
    )
    GO
    
    INSERT INTO [dbo].[Users] ([Name], [Email], [IsActive], [CreatedDate])
    VALUES 
        (N'张三', N'<EMAIL>', 1, GETDATE()),
        (N'李四', N'<EMAIL>', 1, GETDATE())
    GO
    """
    
    print("原始SQL Server语句:")
    print("-" * 50)
    print(sqlserver_sql)
    
    # 执行转换
    mariadb_sql = converter.convert_sql(sqlserver_sql)
    
    print("\n转换后的MariaDB语句:")
    print("-" * 50)
    print(mariadb_sql)


def example_file_conversion():
    """示例：转换SQL文件"""
    print("\n=== 文件转换示例 ===")
    
    # 创建一个示例SQL文件
    sample_sql = """
    USE [TestDB]
    GO
    
    SET ANSI_NULLS ON
    GO
    SET QUOTED_IDENTIFIER ON
    GO
    
    CREATE TABLE [dbo].[Products](
        [ProductID] [int] IDENTITY(1,1) NOT NULL,
        [ProductName] [nvarchar](100) NOT NULL,
        [Description] [ntext] NULL,
        [Price] [money] NOT NULL,
        [IsAvailable] [bit] NOT NULL DEFAULT ((1)),
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
        [ModifiedDate] [datetime2](7) NULL,
        [CategoryID] [int] NULL,
        CONSTRAINT [PK_Products] PRIMARY KEY CLUSTERED ([ProductID] ASC)
    )
    GO
    
    CREATE TABLE [dbo].[Categories](
        [CategoryID] [int] IDENTITY(1,1) NOT NULL,
        [CategoryName] [nvarchar](50) NOT NULL,
        [Description] [nvarchar](255) NULL,
        CONSTRAINT [PK_Categories] PRIMARY KEY CLUSTERED ([CategoryID] ASC)
    )
    GO
    
    ALTER TABLE [dbo].[Products]
    ADD CONSTRAINT [FK_Products_Categories] FOREIGN KEY([CategoryID])
    REFERENCES [dbo].[Categories] ([CategoryID])
    GO
    
    INSERT INTO [dbo].[Categories] ([CategoryName], [Description])
    VALUES 
        (N'电子产品', N'各种电子设备'),
        (N'图书', N'各类书籍')
    GO
    
    INSERT INTO [dbo].[Products] ([ProductName], [Description], [Price], [CategoryID])
    VALUES 
        (N'笔记本电脑', N'高性能笔记本电脑', 5999.00, 1),
        (N'Python编程书', N'Python学习教材', 89.90, 2)
    GO
    """
    
    # 保存示例文件
    input_file = "sample_sqlserver.sql"
    with open(input_file, 'w', encoding='utf-8') as f:
        f.write(sample_sql)
    
    print(f"创建示例文件: {input_file}")
    
    # 创建转换器并转换文件
    converter = SQLServerToMariaDBConverter()
    output_file = converter.convert_file(input_file)
    
    print(f"转换完成，输出文件: {output_file}")
    
    # 显示转换结果
    with open(output_file, 'r', encoding='utf-8') as f:
        converted_content = f.read()
    
    print("\n转换后的内容:")
    print("-" * 50)
    print(converted_content)


def example_batch_conversion():
    """示例：批量转换多个文件"""
    print("\n=== 批量转换示例 ===")
    
    import os
    import glob
    
    # 查找当前目录下的所有.sql文件
    sql_files = glob.glob("*.sql")
    
    if not sql_files:
        print("当前目录下没有找到.sql文件")
        return
    
    print(f"找到 {len(sql_files)} 个SQL文件:")
    for file in sql_files:
        print(f"  - {file}")
    
    # 创建转换器
    converter = SQLServerToMariaDBConverter()
    
    # 批量转换
    converted_files = []
    for sql_file in sql_files:
        try:
            # 跳过已经转换过的文件
            if "_mariadb.sql" in sql_file:
                continue
                
            print(f"\n正在转换: {sql_file}")
            output_file = converter.convert_file(sql_file)
            converted_files.append(output_file)
            
        except Exception as e:
            print(f"转换 {sql_file} 时出错: {str(e)}")
    
    print(f"\n批量转换完成! 共转换了 {len(converted_files)} 个文件:")
    for file in converted_files:
        print(f"  - {file}")


def main():
    """主函数"""
    print("SQL Server to MariaDB Converter - 使用示例")
    print("=" * 60)
    
    try:
        # 示例1: 字符串转换
        example_string_conversion()
        
        # 示例2: 文件转换
        example_file_conversion()
        
        # 示例3: 批量转换
        example_batch_conversion()
        
        print("\n" + "=" * 60)
        print("所有示例执行完成!")
        print("您可以查看生成的文件来了解转换效果。")
        
    except Exception as e:
        print(f"执行示例时出错: {str(e)}")


if __name__ == '__main__':
    main()
