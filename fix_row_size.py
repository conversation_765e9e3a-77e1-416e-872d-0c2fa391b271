#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MariaDB行大小限制问题
将大字段转换为TEXT或BLOB类型
"""

import re
import os

def fix_row_size_issues(input_file, output_file=None):
    """修复行大小过大的问题"""
    
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_fixed.sql"
    
    print(f"修复文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("开始修复行大小问题...")
    
    fixes_applied = []
    
    # 1. 将大的VARCHAR字段改为TEXT
    # VARCHAR(255) 及以上的字段改为TEXT
    large_varchar_pattern = r'VARCHAR\(([3-9]\d{2,}|\d{4,})\)'  # 300以上的数字
    if re.search(large_varchar_pattern, content):
        content = re.sub(large_varchar_pattern, 'TEXT', content)
        fixes_applied.append("大VARCHAR字段 → TEXT")
    
    # 2. 将所有VARCHAR(255)改为TEXT（如果表字段太多）
    varchar255_pattern = r'VARCHAR\(255\)'
    varchar255_count = len(re.findall(varchar255_pattern, content))
    if varchar255_count > 100:  # 如果有很多VARCHAR(255)字段
        content = re.sub(varchar255_pattern, 'TEXT', content)
        fixes_applied.append(f"VARCHAR(255) → TEXT ({varchar255_count}个)")
    
    # 3. 优化特定的大表
    # 查找可能有问题的表（字段数量多的表）
    table_patterns = [
        # 找到字段数超过50的表，将VARCHAR改小或改为TEXT
        (r'(CREATE TABLE `[^`]+` \([^)]*(?:VARCHAR\([^)]*\)[^)]*){50,}[^)]*\))', 'large_table'),
    ]
    
    # 4. 针对性修复：将一些明显的大字段改为TEXT
    field_fixes = [
        (r'`([^`]*(?:desc|description|remark|note|memo|content|text)[^`]*)` VARCHAR\(\d+\)', r'`\1` TEXT'),
        (r'`([^`]*(?:name|title)[^`]*)` VARCHAR\(([2-9]\d{2,}|\d{4,})\)', r'`\1` VARCHAR(100)'),
        (r'`([^`]*(?:address|addr)[^`]*)` VARCHAR\(\d+\)', r'`\1` TEXT'),
        (r'`([^`]*(?:path|url|link)[^`]*)` VARCHAR\(\d+\)', r'`\1` TEXT'),
    ]
    
    for pattern, replacement in field_fixes:
        if re.search(pattern, content, re.IGNORECASE):
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            fixes_applied.append(f"字段类型优化: {pattern[:30]}...")
    
    # 5. 激进修复：对于有很多字段的表，将大部分VARCHAR改为TEXT
    def fix_large_table(match):
        table_sql = match.group(0)
        # 计算VARCHAR字段数量
        varchar_count = len(re.findall(r'VARCHAR\(\d+\)', table_sql))
        
        if varchar_count > 30:  # 如果VARCHAR字段超过30个
            # 将VARCHAR(100)以上的改为TEXT
            table_sql = re.sub(r'VARCHAR\(([1-9]\d{2,}|\d{4,})\)', 'TEXT', table_sql)
            # 将一些VARCHAR(255)改为VARCHAR(100)
            table_sql = re.sub(r'VARCHAR\(255\)', 'VARCHAR(100)', table_sql)
            
        return table_sql
    
    # 应用到所有CREATE TABLE语句
    content = re.sub(r'CREATE TABLE `[^`]+` \([^)]+\);', fix_large_table, content, flags=re.DOTALL)
    fixes_applied.append("大表字段优化")
    
    # 6. 最后的安全措施：确保没有超大的VARCHAR
    content = re.sub(r'VARCHAR\(([5-9]\d{3,}|\d{5,})\)', 'LONGTEXT', content)
    
    # 7. 优化数据类型组合
    # 如果一个表有太多TEXT字段，将一些改为VARCHAR
    def balance_text_fields(match):
        table_sql = match.group(0)
        text_count = table_sql.count(' TEXT')
        varchar_count = len(re.findall(r'VARCHAR\(\d+\)', table_sql))
        
        # 如果TEXT字段太多，将一些小的TEXT改回VARCHAR
        if text_count > 20:
            # 将一些TEXT改为VARCHAR(500)
            table_sql = re.sub(r'TEXT', 'VARCHAR(500)', table_sql, count=text_count//2)
        
        return table_sql
    
    content = re.sub(r'CREATE TABLE `[^`]+` \([^)]+\);', balance_text_fields, content, flags=re.DOTALL)
    
    # 写入修复后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("\n修复完成!")
    print("=" * 50)
    if fixes_applied:
        print("应用的修复:")
        for fix in fixes_applied:
            print(f"  ✓ {fix}")
    else:
        print("  - 未发现需要修复的问题")
    
    print("=" * 50)
    print(f"修复后的文件: {output_file}")
    
    return output_file

def create_optimized_create_tables():
    """创建优化的CREATE语句，专门解决行大小问题"""
    
    optimized_sql = """-- 优化的CREATE语句，解决行大小限制问题
-- 将大字段改为TEXT类型，减少行大小

-- 设置会话变量
SET SESSION sql_mode = 'NO_ENGINE_SUBSTITUTION';
SET SESSION innodb_strict_mode = 0;

"""
    
    # 读取原始CREATE文件
    with open('create_all_tables.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 应用优化
    print("创建优化版本...")
    
    # 1. 将所有VARCHAR(255)改为TEXT
    content = re.sub(r'VARCHAR\(255\)', 'TEXT', content)
    
    # 2. 将大VARCHAR改为TEXT
    content = re.sub(r'VARCHAR\(([3-9]\d{2,}|\d{4,})\)', 'TEXT', content)
    
    # 3. 将VARCHAR(100)以上的改为TEXT（激进优化）
    content = re.sub(r'VARCHAR\(([1-9]\d{2,})\)', 'TEXT', content)
    
    # 4. 保留小的VARCHAR
    content = re.sub(r'VARCHAR\(([1-9]\d?)\)', r'VARCHAR(\1)', content)  # 保留1-99的VARCHAR
    
    # 5. 添加ROW_FORMAT=DYNAMIC
    content = re.sub(r'ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;', 
                    'ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;', 
                    content)
    
    optimized_sql += content
    
    # 保存优化版本
    output_file = "create_all_tables_optimized.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(optimized_sql)
    
    print(f"优化版本已保存: {output_file}")
    return output_file

def main():
    """主函数"""
    print("=" * 60)
    print("修复MariaDB行大小限制问题")
    print("=" * 60)
    
    # 方法1: 修复现有文件
    if os.path.exists('create_all_tables.sql'):
        print("方法1: 修复现有CREATE文件")
        fixed_file = fix_row_size_issues('create_all_tables.sql')
        print(f"修复完成: {fixed_file}")
    
    print("\n" + "=" * 60)
    
    # 方法2: 创建激进优化版本
    print("方法2: 创建激进优化版本")
    optimized_file = create_optimized_create_tables()
    
    print("\n" + "=" * 60)
    print("修复完成!")
    print("现在有以下文件可用:")
    print("1. create_all_tables_fixed.sql - 保守修复版本")
    print("2. create_all_tables_optimized.sql - 激进优化版本（推荐）")
    print("\n建议使用优化版本:")
    print("mysql -u root -p rent < create_all_tables_optimized.sql")
    print("=" * 60)

if __name__ == '__main__':
    main()
