#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Server to MariaDB Converter - GUI版本
使用tkinter创建图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from sqlserver_to_mariadb_converter import SQLServerToMariaDBConverter


class SQLConverterGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("SQL Server to MariaDB Converter")
        self.root.geometry("800x600")
        
        # 创建转换器实例
        self.converter = SQLServerToMariaDBConverter()
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="SQL Server to MariaDB Converter", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 输入文件
        ttk.Label(file_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.input_file_var = tk.StringVar()
        input_entry = ttk.Entry(file_frame, textvariable=self.input_file_var)
        input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(file_frame, text="浏览", command=self.browse_input_file).grid(row=0, column=2)
        
        # 输出文件
        ttk.Label(file_frame, text="输出文件:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.output_file_var = tk.StringVar()
        output_entry = ttk.Entry(file_frame, textvariable=self.output_file_var)
        output_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        ttk.Button(file_frame, text="浏览", command=self.browse_output_file).grid(row=1, column=2, pady=(10, 0))
        
        # 选项区域
        options_frame = ttk.LabelFrame(main_frame, text="转换选项", padding="10")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.verbose_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="显示详细转换信息", 
                       variable=self.verbose_var).grid(row=0, column=0, sticky=tk.W)
        
        self.backup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="自动备份原文件", 
                       variable=self.backup_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        self.convert_button = ttk.Button(button_frame, text="开始转换", 
                                        command=self.start_conversion, style='Accent.TButton')
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def browse_input_file(self):
        """浏览输入文件"""
        filename = filedialog.askopenfilename(
            title="选择SQL Server备份文件",
            filetypes=[("SQL文件", "*.sql"), ("所有文件", "*.*")]
        )
        if filename:
            self.input_file_var.set(filename)
            # 自动设置输出文件名
            if not self.output_file_var.get():
                base_name = os.path.splitext(filename)[0]
                output_name = f"{base_name}_mariadb.sql"
                self.output_file_var.set(output_name)
    
    def browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="保存MariaDB兼容文件",
            defaultextension=".sql",
            filetypes=[("SQL文件", "*.sql"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def start_conversion(self):
        """开始转换（在新线程中执行）"""
        input_file = self.input_file_var.get().strip()
        output_file = self.output_file_var.get().strip()
        
        if not input_file:
            messagebox.showerror("错误", "请选择输入文件")
            return
        
        if not os.path.exists(input_file):
            messagebox.showerror("错误", f"输入文件不存在: {input_file}")
            return
        
        if not output_file:
            messagebox.showerror("错误", "请指定输出文件")
            return
        
        # 在新线程中执行转换
        self.convert_button.config(state='disabled')
        self.progress.start()
        
        thread = threading.Thread(target=self.perform_conversion, 
                                args=(input_file, output_file))
        thread.daemon = True
        thread.start()
    
    def perform_conversion(self, input_file, output_file):
        """执行转换"""
        try:
            self.log_message("=" * 60)
            self.log_message(f"开始转换: {input_file}")
            self.log_message(f"输出文件: {output_file}")
            self.log_message("=" * 60)
            
            # 备份原文件
            if self.backup_var.get():
                backup_file = f"{input_file}.backup"
                import shutil
                shutil.copy2(input_file, backup_file)
                self.log_message(f"已备份原文件: {backup_file}")
            
            # 读取输入文件
            self.log_message("正在读取输入文件...")
            try:
                with open(input_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
            except UnicodeDecodeError:
                with open(input_file, 'r', encoding='gbk') as f:
                    sql_content = f.read()
                self.log_message("使用GBK编码读取文件")
            
            # 执行转换
            if self.verbose_var.get():
                # 重定向转换器的输出到GUI
                original_print = print
                def gui_print(*args, **kwargs):
                    message = ' '.join(str(arg) for arg in args)
                    self.root.after(0, self.log_message, message)
                
                # 临时替换print函数
                import builtins
                builtins.print = gui_print
                
                try:
                    converted_sql = self.converter.convert_sql(sql_content)
                finally:
                    builtins.print = original_print
            else:
                converted_sql = self.converter.convert_sql(sql_content)
            
            # 写入输出文件
            self.log_message("正在写入输出文件...")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(converted_sql)
            
            self.log_message("=" * 60)
            self.log_message("转换完成!")
            self.log_message(f"输出文件: {output_file}")
            self.log_message(f"文件大小: {os.path.getsize(output_file)} 字节")
            self.log_message("=" * 60)
            
            # 显示成功消息
            self.root.after(0, lambda: messagebox.showinfo("成功", 
                f"转换完成!\n\n输入文件: {input_file}\n输出文件: {output_file}"))
            
        except Exception as e:
            error_msg = f"转换失败: {str(e)}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        
        finally:
            # 恢复界面状态
            self.root.after(0, self.conversion_finished)
    
    def conversion_finished(self):
        """转换完成后的清理工作"""
        self.progress.stop()
        self.convert_button.config(state='normal')


def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置样式
    style = ttk.Style()
    if 'Accent.TButton' not in style.theme_names():
        style.configure('Accent.TButton', foreground='white', background='#0078d4')
    
    app = SQLConverterGUI(root)
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()


if __name__ == '__main__':
    main()
