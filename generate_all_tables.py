#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析整个SQL文件，生成所有表的CREATE语句
"""

import re
import os

def analyze_all_tables(sql_file):
    """分析整个SQL文件中的所有表"""
    
    print(f"分析文件: {sql_file}")
    file_size = os.path.getsize(sql_file)
    print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    
    tables = {}
    processed_size = 0
    
    # 分块读取文件
    chunk_size = 1024 * 1024  # 1MB chunks
    
    with open(sql_file, 'r', encoding='utf-8') as f:
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                break
                
            processed_size += len(chunk)
            progress = (processed_size / file_size) * 100
            print(f"\r处理进度: {progress:.1f}%", end="", flush=True)
            
            # 查找INSERT语句
            insert_pattern = r'INSERT INTO `?([^`\s]+)`?\s*\(([^)]+)\)\s*VALUES'
            matches = re.findall(insert_pattern, chunk, re.IGNORECASE)
            
            for table_name, columns_str in matches:
                if table_name not in tables:
                    # 清理列名
                    columns = [col.strip().strip('[]`') for col in columns_str.split(',')]
                    tables[table_name] = columns
    
    print(f"\n发现 {len(tables)} 个表:")
    for i, (table_name, columns) in enumerate(tables.items(), 1):
        print(f"{i:3d}. {table_name} ({len(columns)} 列)")
    
    return tables

def analyze_sample_data(sql_file, table_name, columns, max_samples=3):
    """分析表的样本数据来推断字段类型"""
    
    print(f"分析 {table_name} 的数据类型...")
    
    # 查找该表的VALUES数据
    pattern = rf'INSERT INTO `?{re.escape(table_name)}`?[^)]+\)\s*VALUES\s*\((.*?)\)'
    
    samples = []
    with open(sql_file, 'r', encoding='utf-8') as f:
        content = f.read(500000)  # 读取前500KB
        matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
        
        for match in matches[:max_samples]:
            # 简单解析VALUES
            values = parse_values(match)
            if values and len(values) >= len(columns):
                samples.append(values[:len(columns)])
    
    if not samples:
        print(f"  未找到 {table_name} 的样本数据，使用默认类型")
        return [(col, 'VARCHAR(255)') for col in columns]
    
    # 分析每个字段的类型
    column_types = []
    for i, col_name in enumerate(columns):
        col_type = infer_column_type(col_name, [sample[i] if i < len(sample) else '' for sample in samples])
        column_types.append((col_name, col_type))
    
    return column_types

def parse_values(values_str):
    """解析VALUES字符串"""
    values = []
    current_value = ""
    in_quotes = False
    quote_char = None
    i = 0
    
    while i < len(values_str):
        char = values_str[i]
        
        if not in_quotes:
            if char in ["'", '"']:
                quote_char = char
                in_quotes = True
                current_value += char
            elif char == 'N' and i + 1 < len(values_str) and values_str[i + 1] == "'":
                quote_char = "'"
                in_quotes = True
                current_value += char
            elif char == ',':
                values.append(current_value.strip())
                current_value = ""
            else:
                current_value += char
        else:
            current_value += char
            if char == quote_char:
                # 检查是否是转义的引号
                if i + 1 < len(values_str) and values_str[i + 1] == quote_char:
                    current_value += values_str[i + 1]
                    i += 1
                else:
                    in_quotes = False
                    quote_char = None
        
        i += 1
    
    if current_value.strip():
        values.append(current_value.strip())
    
    return values

def infer_column_type(col_name, sample_values):
    """推断字段类型"""
    
    col_name_lower = col_name.lower()
    
    # 根据字段名推断
    if any(keyword in col_name_lower for keyword in ['date', 'time']):
        return 'DATETIME'
    elif any(keyword in col_name_lower for keyword in ['id', 'no', 'num']) and col_name_lower.endswith(('id', 'no')):
        return 'VARCHAR(50)'
    elif any(keyword in col_name_lower for keyword in ['name', 'title', 'desc']):
        return 'VARCHAR(255)'
    elif any(keyword in col_name_lower for keyword in ['text', 'content', 'remark', 'note']):
        return 'TEXT'
    
    # 根据样本数据推断
    for value in sample_values:
        if not value or value.upper() in ['NULL', '']:
            continue
            
        value = value.strip()
        
        # 字符串
        if value.startswith(("'", "N'")):
            content = value[2:-1] if value.startswith("N'") else value[1:-1]
            if len(content) <= 50:
                return 'VARCHAR(100)'
            elif len(content) <= 255:
                return 'VARCHAR(255)'
            else:
                return 'TEXT'
        
        # 数字
        elif value.replace('-', '').replace('.', '').isdigit():
            if '.' in value:
                return 'DECIMAL(18,2)'
            else:
                try:
                    num = int(value)
                    if -128 <= num <= 127:
                        return 'TINYINT'
                    elif -32768 <= num <= 32767:
                        return 'SMALLINT'
                    elif -2147483648 <= num <= 2147483647:
                        return 'INT'
                    else:
                        return 'BIGINT'
                except:
                    return 'VARCHAR(50)'
    
    return 'VARCHAR(255)'

def generate_create_table_sql(table_name, column_types):
    """生成CREATE TABLE语句"""
    
    sql = f"-- 表: {table_name}\n"
    sql += f"CREATE TABLE `{table_name}` (\n"
    
    for i, (col_name, col_type) in enumerate(column_types):
        comma = "," if i < len(column_types) - 1 else ""
        
        # 添加默认值
        default_value = ""
        if 'DECIMAL' in col_type or col_type in ['TINYINT', 'SMALLINT', 'INT', 'BIGINT']:
            default_value = " DEFAULT 0"
        elif col_type == 'DATETIME':
            default_value = " NULL"
        
        sql += f"    `{col_name}` {col_type}{default_value}{comma}\n"
    
    sql += ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n"
    
    return sql

def main():
    """主函数"""
    sql_file = "export_202511111009_clean.sql"
    
    if not os.path.exists(sql_file):
        print(f"文件不存在: {sql_file}")
        return
    
    print("=" * 70)
    print("生成所有表的CREATE语句")
    print("=" * 70)
    
    # 分析所有表
    tables = analyze_all_tables(sql_file)
    
    if not tables:
        print("未找到表信息")
        return
    
    print(f"\n开始生成 {len(tables)} 个表的CREATE语句...")
    
    # 生成所有CREATE语句
    all_create_sql = []
    all_create_sql.append("-- 自动生成的所有表结构\n")
    all_create_sql.append("-- 请在导入数据前先执行这些CREATE TABLE语句\n\n")
    
    for i, (table_name, columns) in enumerate(tables.items(), 1):
        print(f"处理表 {i}/{len(tables)}: {table_name}")
        
        # 分析数据类型
        column_types = analyze_sample_data(sql_file, table_name, columns)
        
        # 生成CREATE语句
        create_sql = generate_create_table_sql(table_name, column_types)
        all_create_sql.append(create_sql)
    
    # 保存所有CREATE语句
    output_file = "create_all_tables.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(all_create_sql)
    
    print(f"\n" + "=" * 70)
    print("生成完成!")
    print(f"所有CREATE语句已保存到: {output_file}")
    print(f"共生成 {len(tables)} 个表的结构")
    
    # 显示表列表
    print(f"\n表列表:")
    for i, table_name in enumerate(tables.keys(), 1):
        print(f"{i:3d}. {table_name}")
    
    print(f"\n导入顺序:")
    print(f"1. 先执行: mysql -u root -p database_name < {output_file}")
    print(f"2. 再执行: mysql -u root -p database_name < {sql_file}")
    print("=" * 70)

if __name__ == '__main__':
    main()
