
    USE [SampleDB]
    GO
    
    SET ANSI_NULLS ON
    GO
    SET QUOTED_IDENTIFIER ON
    GO
    
    -- 创建用户表
    CREATE TABLE [dbo].[Users](
        [UserID] [int] IDENTITY(1,1) NOT NULL,
        [Username] [nvarchar](50) NOT NULL,
        [Email] [nvarchar](100) NOT NULL,
        [Password] [nvarchar](255) NOT NULL,
        [FirstName] [nvarchar](50) NULL,
        [LastName] [nvarchar](50) NULL,
        [IsActive] [bit] NOT NULL DEFAULT ((1)),
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
        [ModifiedDate] [datetime2](7) NULL,
        [UserGuid] [uniqueidentifier] NOT NULL DEFAULT (newid()),
        [ProfileImage] [image] NULL,
        [Balance] [money] NOT NULL DEFAULT ((0.00)),
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([UserID] ASC)
    )
    GO
    
    -- 创建分类表
    CREATE TABLE [dbo].[Categories](
        [CategoryID] [int] IDENTITY(1,1) NOT NULL,
        [CategoryName] [nvarchar](100) NOT NULL,
        [Description] [ntext] NULL,
        [IsActive] [bit] NOT NULL DEFAULT ((1)),
        CONSTRAINT [PK_Categories] PRIMARY KEY CLUSTERED ([CategoryID] ASC)
    )
    GO
    
    -- 插入测试数据
    SET IDENTITY_INSERT [dbo].[Users] ON
    GO
    
    INSERT INTO [dbo].[Users] ([UserID], [Username], [Email], [Password], [FirstName], [LastName])
    VALUES 
        (1, N'admin', N'<EMAIL>', N'password123', N'管理员', N'用户'),
        (2, N'user1', N'<EMAIL>', N'password456', N'张', N'三'),
        (3, N'user2', N'<EMAIL>', N'password789', N'李', N'四')
    GO
    
    SET IDENTITY_INSERT [dbo].[Users] OFF
    GO
    
    INSERT INTO [dbo].[Categories] ([CategoryName], [Description])
    VALUES 
        (N'电子产品', N'各种电子设备和配件'),
        (N'图书', N'各类书籍和教材'),
        (N'服装', N'男女服装和配饰')
    GO
    