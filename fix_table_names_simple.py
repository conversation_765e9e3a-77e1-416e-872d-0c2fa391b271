#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化表名转换器
将三段式表名 database.schema.table 转换为 table
将两段式表名 database.table 转换为 table
不指定目标数据库，只保留最终的表名
"""

import re
import os
import sys


def fix_table_names_simple(input_file, output_file=None):
    """简化表名格式，只保留最终表名"""
    
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 - {input_file}")
        return False
    
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_simple.sql"
    
    file_size = os.path.getsize(input_file)
    print(f"转换文件: {input_file}")
    print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    print(f"输出文件: {output_file}")
    
    try:
        # 读取文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        with open(input_file, 'r', encoding='gbk') as f:
            content = f.read()
        print("使用GBK编码读取文件")
    
    print("开始简化表名...")
    
    # 统计修复信息
    fixes_applied = []
    
    # 1. 处理三段式表名 (database.schema.table) → table
    three_part_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\.(dbo|[a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)\b'
    
    matches = re.findall(three_part_pattern, content)
    if matches:
        print(f"发现 {len(matches)} 个三段式表名")
        unique_tables = set(matches)
        print(f"唯一表名数量: {len(unique_tables)}")
        
        # 显示前10个示例
        print("示例转换:")
        for i, (db, schema, table) in enumerate(list(unique_tables)[:10]):
            print(f"  {db}.{schema}.{table} → `{table}`")
        if len(unique_tables) > 10:
            print(f"  ... 还有 {len(unique_tables) - 10} 个")
        
        # 替换三段式表名为只保留表名
        replacement = r'`\3`'
        content = re.sub(three_part_pattern, replacement, content)
        fixes_applied.append(f"三段式表名 → 表名 ({len(matches)} 处)")
    
    # 2. 处理两段式表名 (database.table) → table
    # 但要排除URL和其他非表名的情况
    two_part_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\.(image_license|xmMeters|[a-zA-Z_][a-zA-Z0-9_]*)\b'
    
    # 先检查是否在SQL语句上下文中
    lines = content.split('\n')
    converted_lines = []
    
    for line_num, line in enumerate(lines):
        if line_num % 50000 == 0:
            print(f"处理第 {line_num:,} 行...")
        
        original_line = line
        
        # 只在SQL语句行中处理两段式表名
        if any(keyword in line.upper() for keyword in ['INSERT INTO', 'CREATE TABLE', 'ALTER TABLE', 'UPDATE', 'DELETE FROM', 'FROM', 'JOIN']):
            # 避免处理URL和其他非表名的情况
            if not any(url_part in line.lower() for url_part in ['http', 'www.', '.com', '.cn', '.net', '.org', 'mailto:', '@']):
                # 查找两段式表名
                two_part_matches = re.findall(two_part_pattern, line)
                if two_part_matches:
                    # 替换两段式表名
                    line = re.sub(two_part_pattern, r'`\2`', line)
        
        converted_lines.append(line)
    
    content = '\n'.join(converted_lines)
    
    if re.search(two_part_pattern, content):
        fixes_applied.append("两段式表名 → 表名")
    
    # 3. 清理重复的反引号
    content = re.sub(r'`{2,}', '`', content)  # 多个反引号变为单个
    content = re.sub(r'``([^`]+)``', r'`\1`', content)  # 双重反引号变为单个
    
    # 4. 确保表名用反引号包围（在SQL语句中）
    # 处理INSERT INTO语句
    content = re.sub(r'INSERT\s+INTO\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', r'INSERT INTO `\1` (', content, flags=re.IGNORECASE)
    
    # 处理CREATE TABLE语句
    content = re.sub(r'CREATE\s+TABLE\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', r'CREATE TABLE `\1` (', content, flags=re.IGNORECASE)
    
    # 处理ALTER TABLE语句
    content = re.sub(r'ALTER\s+TABLE\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+', r'ALTER TABLE `\1` ', content, flags=re.IGNORECASE)
    
    # 5. 再次清理重复的反引号
    content = re.sub(r'`{2,}', '`', content)
    content = re.sub(r'``([^`]+)``', r'`\1`', content)
    
    # 写入修复后的文件
    print("写入转换后的文件...")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    output_size = os.path.getsize(output_file)
    
    # 显示转换结果
    print("\n转换完成!")
    print("=" * 60)
    print(f"输出文件: {output_file}")
    print(f"输出大小: {output_size:,} 字节 ({output_size/1024/1024:.2f} MB)")
    
    if fixes_applied:
        print("\n应用的转换:")
        for fix in fixes_applied:
            print(f"  ✓ {fix}")
    else:
        print("\n  - 未发现需要转换的表名")
    
    print("=" * 60)
    
    return True


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python fix_table_names_simple.py <input_file> [output_file]")
        print("")
        print("功能:")
        print("  - 将 database.schema.table 转换为 `table`")
        print("  - 将 database.table 转换为 `table`")
        print("  - 不指定目标数据库")
        print("  - 保持原有的数据完整性")
        print("")
        print("示例:")
        print("  python fix_table_names_simple.py export_202511111009_mariadb.sql")
        print("  python fix_table_names_simple.py export_202511111009_mariadb.sql clean_export.sql")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        success = fix_table_names_simple(input_file, output_file)
        if success:
            output_name = output_file or input_file.replace('.sql', '_simple.sql')
            print("\n使用建议:")
            print("1. 在导入前先创建目标数据库:")
            print("   CREATE DATABASE your_database_name;")
            print("2. 切换到目标数据库:")
            print("   USE your_database_name;")
            print(f"3. 导入数据:")
            print(f"   SOURCE {output_name};")
            print("   或")
            print(f"   mysql -u username -p database_name < {output_name}")
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
