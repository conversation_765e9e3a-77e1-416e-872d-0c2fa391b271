#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复二进制数据问题
将包含二进制数据的字段改为BLOB类型
"""

import re
import os

def identify_binary_fields(sql_file):
    """识别包含二进制数据的字段"""
    
    print(f"分析文件中的二进制数据: {sql_file}")
    
    binary_fields = set()
    
    # 读取文件的一部分来分析
    with open(sql_file, 'r', encoding='utf-8') as f:
        chunk_size = 1024 * 1024  # 1MB chunks
        processed = 0
        
        while processed < 50 * 1024 * 1024:  # 分析前50MB
            try:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                
                processed += len(chunk)
                progress = min(processed / (50 * 1024 * 1024) * 100, 100)
                print(f"\r分析进度: {progress:.1f}%", end="", flush=True)
                
                # 查找包含二进制数据的INSERT语句
                # 寻找包含 0x 开头的十六进制数据的字段
                hex_pattern = r"INSERT INTO `([^`]+)`[^)]+\)\s*VALUES\s*\([^)]*0x[0-9A-Fa-f]{10,}[^)]*\)"
                matches = re.findall(hex_pattern, chunk, re.IGNORECASE)
                
                for table_name in matches:
                    binary_fields.add(table_name)
                
                # 查找特定的二进制数据模式
                binary_pattern = r"INSERT INTO `([^`]+)`[^)]+\)\s*VALUES\s*\([^)]*\\xFF\\xFF[^)]*\)"
                matches = re.findall(binary_pattern, chunk, re.IGNORECASE)
                
                for table_name in matches:
                    binary_fields.add(table_name)
                    
            except UnicodeDecodeError:
                # 如果遇到编码问题，跳过这个chunk
                continue
    
    print(f"\n发现包含二进制数据的表: {len(binary_fields)}")
    for table in sorted(binary_fields):
        print(f"  - {table}")
    
    return binary_fields

def fix_binary_data_types(create_file, binary_tables, output_file=None):
    """修复包含二进制数据的表的字段类型"""
    
    if output_file is None:
        base_name = os.path.splitext(create_file)[0]
        output_file = f"{base_name}_binary_fixed.sql"
    
    print(f"\n修复CREATE文件: {create_file}")
    print(f"输出文件: {output_file}")
    
    with open(create_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    fixes_applied = []
    
    # 针对包含二进制数据的表，将可能的字段改为BLOB
    for table_name in binary_tables:
        print(f"修复表: {table_name}")
        
        # 查找该表的CREATE语句
        table_pattern = rf'(CREATE TABLE `{re.escape(table_name)}` \([^)]+\);)'
        table_match = re.search(table_pattern, content, re.DOTALL)
        
        if table_match:
            table_sql = table_match.group(1)
            original_sql = table_sql
            
            # 将可能包含二进制数据的字段改为BLOB
            # 常见的二进制字段名模式
            binary_field_patterns = [
                r'`([^`]*(?:memo|data|content|image|file|binary|blob)[^`]*)` TEXT',
                r'`([^`]*(?:report|document|attachment)[^`]*)` TEXT',
                r'`(ReportMemo)` TEXT',  # 特定字段
                r'`([^`]*Memo)` TEXT',   # 以Memo结尾的字段
            ]
            
            for pattern in binary_field_patterns:
                if re.search(pattern, table_sql, re.IGNORECASE):
                    table_sql = re.sub(pattern, r'`\1` LONGBLOB', table_sql, flags=re.IGNORECASE)
            
            # 如果有修改，替换原内容
            if table_sql != original_sql:
                content = content.replace(original_sql, table_sql)
                fixes_applied.append(f"修复表 {table_name} 的二进制字段")
    
    # 通用修复：将明显的二进制字段改为BLOB
    general_fixes = [
        (r'`([^`]*(?:image|picture|photo|pic)[^`]*)` TEXT', r'`\1` LONGBLOB'),
        (r'`([^`]*(?:file|document|doc)[^`]*)` TEXT', r'`\1` LONGBLOB'),
        (r'`([^`]*(?:data|binary|blob)[^`]*)` TEXT', r'`\1` LONGBLOB'),
        (r'`([^`]*(?:content|memo)[^`]*)` TEXT', r'`\1` LONGBLOB'),
        (r'`([^`]*(?:attachment|upload)[^`]*)` TEXT', r'`\1` LONGBLOB'),
        (r'`(ReportMemo|reportmemo)` TEXT', r'`\1` LONGBLOB'),  # 特定问题字段
    ]
    
    for pattern, replacement in general_fixes:
        if re.search(pattern, content, re.IGNORECASE):
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            fixes_applied.append(f"通用修复: {pattern[:30]}...")
    
    # 写入修复后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n修复完成!")
    print("=" * 50)
    if fixes_applied:
        print("应用的修复:")
        for fix in fixes_applied:
            print(f"  ✓ {fix}")
    else:
        print("  - 未发现需要修复的二进制字段")
    
    print("=" * 50)
    print(f"修复后的文件: {output_file}")
    
    return output_file

def create_binary_safe_version():
    """创建二进制安全版本"""
    
    print("创建二进制安全版本...")
    
    input_file = "create_all_tables_optimized.sql"
    output_file = "create_all_tables_binary_safe.sql"
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 激进修复：将所有可能包含二进制数据的TEXT字段改为LONGBLOB
    binary_patterns = [
        # 根据字段名判断
        (r'`([^`]*(?:memo|data|content|image|file|binary|blob|report|document|attachment|upload|picture|photo)[^`]*)` TEXT', r'`\1` LONGBLOB'),
        # 特定问题字段
        (r'`(ReportMemo|reportmemo|DataMemo|ContentData|FileData|ImageData)` TEXT', r'`\1` LONGBLOB'),
    ]
    
    fixes = []
    for pattern, replacement in binary_patterns:
        matches = len(re.findall(pattern, content, re.IGNORECASE))
        if matches > 0:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            fixes.append(f"TEXT → LONGBLOB: {matches} 个字段")
    
    # 添加二进制安全设置
    header = """-- 二进制安全版本的CREATE语句
-- 将可能包含二进制数据的字段改为LONGBLOB类型

-- 设置会话变量
SET SESSION sql_mode = 'NO_ENGINE_SUBSTITUTION';
SET SESSION innodb_strict_mode = 0;
SET NAMES utf8mb4;

"""
    
    content = header + content
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"二进制安全版本已保存: {output_file}")
    if fixes:
        print("应用的修复:")
        for fix in fixes:
            print(f"  ✓ {fix}")
    
    return output_file

def main():
    """主函数"""
    print("=" * 60)
    print("修复二进制数据问题")
    print("=" * 60)
    
    # 方法1: 分析数据文件中的二进制数据
    data_file = "export_202511111009_clean.sql"
    if os.path.exists(data_file):
        print("方法1: 分析数据文件中的二进制数据")
        binary_tables = identify_binary_fields(data_file)
        
        if binary_tables and os.path.exists("create_all_tables_optimized.sql"):
            print("\n基于分析结果修复CREATE文件...")
            fixed_file = fix_binary_data_types("create_all_tables_optimized.sql", binary_tables)
            print(f"基于分析的修复完成: {fixed_file}")
    
    print("\n" + "=" * 60)
    
    # 方法2: 创建二进制安全版本（推荐）
    print("方法2: 创建二进制安全版本（推荐）")
    safe_file = create_binary_safe_version()
    
    print("\n" + "=" * 60)
    print("修复完成!")
    print("现在有以下文件可用:")
    print("1. create_all_tables_binary_safe.sql - 二进制安全版本（推荐）")
    if os.path.exists("create_all_tables_optimized_binary_fixed.sql"):
        print("2. create_all_tables_optimized_binary_fixed.sql - 基于分析的修复版本")
    
    print("\n建议使用二进制安全版本:")
    print("mysql -u root -p rent < create_all_tables_binary_safe.sql")
    print("=" * 60)

if __name__ == '__main__':
    main()
