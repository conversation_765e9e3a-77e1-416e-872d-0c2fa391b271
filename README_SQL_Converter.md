# SQL Server to MariaDB Converter

这个脚本可以帮助您将SQL Server导出的备份SQL文件转换为MariaDB兼容的格式。

## 文件说明

- `sqlserver_to_mariadb_converter.py` - 主转换器脚本
- `sql_converter_gui.py` - GUI图形界面版本
- `example_usage.py` - 使用示例脚本
- `test_converter.py` - 测试脚本
- `demo_converter.py` - 完整演示脚本
- `convert_sql.bat` - Windows批处理脚本
- `README_SQL_Converter.md` - 本说明文档

## 功能特性

### 数据类型转换
- `NVARCHAR(n)` → `VARCHAR(n)`
- `NVARCHAR(MAX)` → `LONGTEXT`
- `NTEXT` → `LONGTEXT`
- `BIT` → `TINYINT(1)`
- `DATETIME2` → `DATETIME`
- `UNIQUEIDENTIFIER` → `VARCHAR(36)`
- `IMAGE` → `LONGBLOB`
- `VARBINARY(MAX)` → `LONGBLOB`
- `MONEY` → `DECIMAL(19,4)`
- `SMALLMONEY` → `DECIMAL(10,4)`

### 语法转换
- 方括号标识符 `[table]` → 反引号 `` `table` ``
- `IDENTITY(1,1)` → `AUTO_INCREMENT`
- 移除 `GO` 语句分隔符
- 移除 `SET IDENTITY_INSERT` 语句
- 转换 `USE [database]` → `USE `database``

### 函数转换
- `GETDATE()` → `NOW()`
- `NEWID()` → `UUID()`
- `LEN()` → `LENGTH()`
- `ISNULL()` → `IFNULL()`

### SQL Server特有语句移除
- `SET ANSI_NULLS ON/OFF`
- `SET QUOTED_IDENTIFIER ON/OFF`
- `SET ANSI_PADDING ON/OFF`
- 其他SET语句

## 使用方法

### 1. 命令行使用

```bash
# 基本用法
python sqlserver_to_mariadb_converter.py input.sql

# 指定输出文件
python sqlserver_to_mariadb_converter.py input.sql -o output_mariadb.sql

# 显示详细信息
python sqlserver_to_mariadb_converter.py input.sql -v
```

### 2. Windows批处理使用

```cmd
# 基本用法
convert_sql.bat input.sql

# 指定输出文件
convert_sql.bat input.sql output.sql

# 启动GUI版本
convert_sql.bat gui
```

### 3. GUI图形界面使用

```bash
python sql_converter_gui.py
```

然后在图形界面中：
1. 选择输入的SQL Server文件
2. 指定输出文件位置
3. 选择转换选项
4. 点击"开始转换"

### 4. Python代码中使用

```python
from sqlserver_to_mariadb_converter import SQLServerToMariaDBConverter

# 创建转换器实例
converter = SQLServerToMariaDBConverter()

# 转换文件
output_file = converter.convert_file('input.sql', 'output.sql')

# 或者直接转换SQL字符串
sql_content = "SELECT * FROM [Users] WHERE [Name] = 'test'"
converted_sql = converter.convert_sql(sql_content)
print(converted_sql)  # SELECT * FROM `Users` WHERE `Name` = 'test'
```

## 转换示例

### 输入 (SQL Server)
```sql
USE [MyDatabase]
GO

SET ANSI_NULLS ON
GO

CREATE TABLE [dbo].[Users](
    [ID] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Email] [nvarchar](100) NULL,
    [IsActive] [bit] NOT NULL DEFAULT ((1)),
    [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
    [UserGuid] [uniqueidentifier] NOT NULL DEFAULT (newid()),
    CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([ID] ASC)
)
GO
```

### 输出 (MariaDB)
```sql
USE `MyDatabase`;

CREATE TABLE `dbo`.`Users`(
    `ID` INT AUTO_INCREMENT NOT NULL,
    `Name` VARCHAR(50) NOT NULL,
    `Email` VARCHAR(100) NULL,
    `IsActive` TINYINT(1) NOT NULL DEFAULT (1),
    `CreatedDate` DATETIME NOT NULL DEFAULT (NOW()),
    `UserGuid` VARCHAR(36) NOT NULL DEFAULT (UUID()),
    PRIMARY KEY (`ID`)
);
```

## 注意事项

1. **备份原文件**: 转换前请备份原始SQL文件
2. **检查结果**: 转换后请仔细检查生成的SQL文件
3. **测试导入**: 建议先在测试环境中导入转换后的SQL
4. **手动调整**: 某些复杂的SQL语句可能需要手动调整

## 常见问题

### 编码问题
脚本会自动尝试UTF-8和GBK编码读取文件。如果遇到编码问题，请确保输入文件使用正确的编码格式。

### 复杂查询
对于包含复杂存储过程、触发器或特殊函数的SQL，可能需要手动调整。

### 数据类型精度
某些数据类型的精度转换可能需要根据实际需求调整。

## 支持的文件格式

- `.sql` 文件
- 支持UTF-8和GBK编码
- 支持大文件处理

## 系统要求

- Python 3.6+
- 无需额外依赖包

## 许可证

MIT License
