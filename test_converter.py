#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Server to MariaDB Converter - 测试脚本
"""

from sqlserver_to_mariadb_converter import SQLServerToMariaDBConverter


def test_data_types():
    """测试数据类型转换"""
    print("=== 测试数据类型转换 ===")
    
    converter = SQLServerToMariaDBConverter()
    
    test_cases = [
        ("NVARCHAR(50)", "VARCHAR(50)"),
        ("NVARCHAR(MAX)", "LONGTEXT"),
        ("BIT", "TINYINT(1)"),
        ("DATETIME2", "DATETIME"),
        ("UNIQUEIDENTIFIER", "VARCHAR(36)"),
        ("IMAGE", "LONGBLOB"),
        ("MONEY", "DECIMAL(19,4)"),
        ("SMALLMONEY", "DECIMAL(10,4)"),
    ]
    
    for input_type, expected in test_cases:
        sql = f"CREATE TABLE test (col {input_type})"
        result = converter.convert_data_types(sql)
        if expected in result:
            print(f"✓ {input_type} → {expected}")
        else:
            print(f"✗ {input_type} 转换失败")
            print(f"  期望: {expected}")
            print(f"  实际: {result}")


def test_identifiers():
    """测试标识符转换"""
    print("\n=== 测试标识符转换 ===")
    
    converter = SQLServerToMariaDBConverter()
    
    test_cases = [
        ("SELECT * FROM [Users]", "SELECT * FROM `Users`"),
        ("CREATE TABLE [dbo].[Products]", "CREATE TABLE `dbo`.`Products`"),
        ("[UserID] [int] NOT NULL", "`UserID` INT NOT NULL"),
    ]
    
    for input_sql, expected in test_cases:
        result = converter.convert_identifiers(input_sql)
        if expected in result:
            print(f"✓ 标识符转换成功")
        else:
            print(f"✗ 标识符转换失败")
            print(f"  输入: {input_sql}")
            print(f"  期望: {expected}")
            print(f"  实际: {result}")


def test_functions():
    """测试函数转换"""
    print("\n=== 测试函数转换 ===")
    
    converter = SQLServerToMariaDBConverter()
    
    test_cases = [
        ("SELECT GETDATE()", "SELECT NOW()"),
        ("DEFAULT (NEWID())", "DEFAULT (UUID())"),
        ("LEN(name)", "LENGTH(name)"),
        ("ISNULL(field, 0)", "IFNULL(field, 0)"),
    ]
    
    for input_sql, expected in test_cases:
        result = converter.convert_functions(input_sql)
        if expected in result:
            print(f"✓ 函数转换成功: {input_sql}")
        else:
            print(f"✗ 函数转换失败")
            print(f"  输入: {input_sql}")
            print(f"  期望: {expected}")
            print(f"  实际: {result}")


def test_identity_columns():
    """测试IDENTITY列转换"""
    print("\n=== 测试IDENTITY列转换 ===")
    
    converter = SQLServerToMariaDBConverter()
    
    test_cases = [
        ("ID INT IDENTITY(1,1)", "ID INT AUTO_INCREMENT"),
        ("SET IDENTITY_INSERT Users ON", ""),
        ("SET IDENTITY_INSERT Users OFF", ""),
    ]
    
    for input_sql, expected in test_cases:
        result = converter.handle_identity_columns(input_sql)
        if expected in result or (expected == "" and expected == result.strip()):
            print(f"✓ IDENTITY转换成功")
        else:
            print(f"✗ IDENTITY转换失败")
            print(f"  输入: {input_sql}")
            print(f"  期望: '{expected}'")
            print(f"  实际: '{result}'")


def test_complete_conversion():
    """测试完整转换"""
    print("\n=== 测试完整转换 ===")
    
    converter = SQLServerToMariaDBConverter()
    
    sqlserver_sql = """
    USE [TestDB]
    GO
    
    SET ANSI_NULLS ON
    GO
    
    CREATE TABLE [dbo].[Users](
        [ID] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](50) NOT NULL,
        [Email] [nvarchar](100) NULL,
        [IsActive] [bit] NOT NULL DEFAULT ((1)),
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
        [UserGuid] [uniqueidentifier] NOT NULL DEFAULT (newid()),
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([ID] ASC)
    )
    GO
    
    INSERT INTO [dbo].[Users] ([Name], [Email])
    VALUES (N'测试用户', N'<EMAIL>')
    GO
    """
    
    print("原始SQL:")
    print("-" * 40)
    print(sqlserver_sql)
    
    result = converter.convert_sql(sqlserver_sql)
    
    print("\n转换结果:")
    print("-" * 40)
    print(result)
    
    # 检查关键转换
    checks = [
        ("USE `TestDB`", "USE语句转换"),
        ("`dbo`.`Users`", "标识符转换"),
        ("AUTO_INCREMENT", "IDENTITY转换"),
        ("VARCHAR(50)", "NVARCHAR转换"),
        ("TINYINT(1)", "BIT转换"),
        ("DATETIME", "DATETIME2转换"),
        ("NOW()", "GETDATE转换"),
        ("UUID()", "NEWID转换"),
    ]
    
    print("\n转换检查:")
    print("-" * 40)
    for check_text, description in checks:
        if check_text in result:
            print(f"✓ {description}")
        else:
            print(f"✗ {description} - 未找到: {check_text}")


def create_test_file():
    """创建测试文件"""
    print("\n=== 创建测试文件 ===")
    
    test_sql = """
    USE [SampleDB]
    GO
    
    SET ANSI_NULLS ON
    GO
    SET QUOTED_IDENTIFIER ON
    GO
    
    -- 创建用户表
    CREATE TABLE [dbo].[Users](
        [UserID] [int] IDENTITY(1,1) NOT NULL,
        [Username] [nvarchar](50) NOT NULL,
        [Email] [nvarchar](100) NOT NULL,
        [Password] [nvarchar](255) NOT NULL,
        [FirstName] [nvarchar](50) NULL,
        [LastName] [nvarchar](50) NULL,
        [IsActive] [bit] NOT NULL DEFAULT ((1)),
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT (getdate()),
        [ModifiedDate] [datetime2](7) NULL,
        [UserGuid] [uniqueidentifier] NOT NULL DEFAULT (newid()),
        [ProfileImage] [image] NULL,
        [Balance] [money] NOT NULL DEFAULT ((0.00)),
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([UserID] ASC)
    )
    GO
    
    -- 创建分类表
    CREATE TABLE [dbo].[Categories](
        [CategoryID] [int] IDENTITY(1,1) NOT NULL,
        [CategoryName] [nvarchar](100) NOT NULL,
        [Description] [ntext] NULL,
        [IsActive] [bit] NOT NULL DEFAULT ((1)),
        CONSTRAINT [PK_Categories] PRIMARY KEY CLUSTERED ([CategoryID] ASC)
    )
    GO
    
    -- 插入测试数据
    SET IDENTITY_INSERT [dbo].[Users] ON
    GO
    
    INSERT INTO [dbo].[Users] ([UserID], [Username], [Email], [Password], [FirstName], [LastName])
    VALUES 
        (1, N'admin', N'<EMAIL>', N'password123', N'管理员', N'用户'),
        (2, N'user1', N'<EMAIL>', N'password456', N'张', N'三'),
        (3, N'user2', N'<EMAIL>', N'password789', N'李', N'四')
    GO
    
    SET IDENTITY_INSERT [dbo].[Users] OFF
    GO
    
    INSERT INTO [dbo].[Categories] ([CategoryName], [Description])
    VALUES 
        (N'电子产品', N'各种电子设备和配件'),
        (N'图书', N'各类书籍和教材'),
        (N'服装', N'男女服装和配饰')
    GO
    """
    
    filename = "test_sqlserver_sample.sql"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(test_sql)
    
    print(f"创建测试文件: {filename}")
    
    # 转换测试文件
    converter = SQLServerToMariaDBConverter()
    output_file = converter.convert_file(filename)
    
    print(f"转换完成: {output_file}")
    
    return filename, output_file


def main():
    """主测试函数"""
    print("SQL Server to MariaDB Converter - 测试")
    print("=" * 60)
    
    try:
        # 运行各项测试
        test_data_types()
        test_identifiers()
        test_functions()
        test_identity_columns()
        test_complete_conversion()
        
        # 创建和转换测试文件
        test_file, output_file = create_test_file()
        
        print("\n" + "=" * 60)
        print("测试完成!")
        print(f"测试文件: {test_file}")
        print(f"转换结果: {output_file}")
        print("\n您可以查看转换结果文件来验证转换效果。")
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
