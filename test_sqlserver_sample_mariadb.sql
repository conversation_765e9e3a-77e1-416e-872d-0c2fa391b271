USE `SampleDB`

    -- 创建用户表
    CREATE TABLE `dbo`.`Users`(
        `UserID` INT AUTO_INCREMENT NOT NULL,
        `Username` `nvarchar`(50) NOT NULL,
        `Email` `nvarchar`(100) NOT NULL,
        `Password` `nvarchar`(255) NOT NULL,
        `FirstName` `nvarchar`(50) NULL,
        `LastName` `nvarchar`(50) NULL,
        `IsActive` `TINYINT(1)` NOT NULL DEFAULT (1),
        `CreatedDate` DATETIME(7) NOT NULL DEFAULT (NOW()),
        `ModifiedDate` DATETIME(7) NULL,
        `UserGuid` `VARCHAR(36)` NOT NULL DEFAULT (UUID()),
        `ProfileImage` LONGBLOB NULL,
        `Balance` `DECIMAL(19,4)` NOT NULL DEFAULT (0.00),
        PRIMARY KEY (`UserID` ASC)
    )

    -- 创建分类表
    CREATE TABLE `dbo`.`Categories`(
        `CategoryID` INT AUTO_INCREMENT NOT NULL,
        `CategoryName` `nvarchar`(100) NOT NULL,
        `Description` LONGTEXT NULL,
        `IsActive` `TINYINT(1)` NOT NULL DEFAULT (1),
        PRIMARY KEY (`CategoryID` ASC)
    )

    -- 插入测试数据

    INSERT INTO `dbo`.`Users` (`UserID`, `Username`, `Email`, `Password`, `FirstName`, `LastName`)
    VALUES
        (1, N'admin', N'<EMAIL>', N'password123', N'管理员', N'用户'),
        (2, N'user1', N'<EMAIL>', N'password456', N'张', N'三'),
        (3, N'user2', N'<EMAIL>', N'password789', N'李', N'四')

    INSERT INTO `dbo`.`Categories` (`CategoryName`, `Description`)
    VALUES
        (N'电子产品', N'各种电子设备和配件'),
        (N'图书', N'各类书籍和教材'),
        (N'服装', N'男女服装和配饰')