-- MariaDB字符集和排序规则检查脚本

-- 1. 检查MariaDB版本
SELECT VERSION() as MariaDB_Version;

-- 2. 检查支持的字符集
SHOW CHARACTER SET LIKE 'utf8%';

-- 3. 检查支持的排序规则
SHOW COLLATION LIKE 'utf8%';

-- 4. 检查当前数据库设置
SELECT 
    @@character_set_database as current_charset,
    @@collation_database as current_collation;

-- 5. 检查服务器默认设置
SELECT 
    @@character_set_server as server_charset,
    @@collation_server as server_collation;

-- 6. 检查客户端设置
SELECT 
    @@character_set_client as client_charset,
    @@character_set_connection as connection_charset,
    @@character_set_results as results_charset;
