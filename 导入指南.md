# MariaDB 导入指南

## 🎉 转换完成！

您的SQL Server备份文件已经成功转换为MariaDB兼容格式！

### 📁 文件信息
- **原始文件**: `export_202511111009.sql` (288.95 MB)
- **最终文件**: `export_202511111009_final.sql` (288.51 MB)
- **目标数据库**: `xscj`

### ✅ 已修复的问题
1. **三段式表名**: `jtpmGp15.dbo.xmMeters` → `xscj.xmMeters`
2. **字符集兼容性**: 移除了不兼容的字符集设置
3. **语法转换**: SQL Server语法 → MariaDB语法
4. **数据类型**: 转换了不兼容的数据类型
5. **标识符**: 方括号 → 反引号

## 🚀 导入步骤

### 步骤1: 创建数据库
```sql
-- 在MariaDB中执行
CREATE DATABASE IF NOT EXISTS xscj DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 如果utf8mb4不支持，使用utf8
-- CREATE DATABASE IF NOT EXISTS xscj DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
```

### 步骤2: 导入数据
```bash
# 方法1: 使用mysql命令行
mysql -u root -p xscj < export_202511111009_final.sql

# 方法2: 使用mariadb命令行
mariadb -u root -p xscj < export_202511111009_final.sql

# 方法3: 在MariaDB客户端中
USE xscj;
SOURCE export_202511111009_final.sql;
```

### 步骤3: 验证导入
```sql
-- 检查数据库
SHOW DATABASES;

-- 使用数据库
USE xscj;

-- 查看表
SHOW TABLES;

-- 检查表结构（以xmMeters为例）
DESCRIBE xmMeters;

-- 检查数据量
SELECT COUNT(*) FROM xmMeters;
```

## ⚠️ 注意事项

### 导入前准备
1. **备份现有数据**: 如果数据库已存在，请先备份
2. **检查磁盘空间**: 确保有足够空间（至少600MB）
3. **检查内存**: 大文件导入可能需要较多内存
4. **设置超时**: 可能需要增加导入超时时间

### MariaDB配置建议
```ini
# 在my.cnf或my.ini中添加
[mysql]
max_allowed_packet = 1024M

[mysqld]
max_allowed_packet = 1024M
innodb_buffer_pool_size = 512M
wait_timeout = 28800
interactive_timeout = 28800
```

### 可能遇到的问题

#### 1. 字符集错误
```sql
-- 如果遇到字符集问题，尝试：
SET NAMES utf8mb4;
-- 或
SET NAMES utf8;
```

#### 2. 包大小限制
```sql
-- 如果遇到包大小限制：
SET GLOBAL max_allowed_packet = 1073741824; -- 1GB
```

#### 3. 导入超时
```sql
-- 增加超时时间：
SET GLOBAL wait_timeout = 28800; -- 8小时
SET GLOBAL interactive_timeout = 28800;
```

#### 4. 内存不足
- 考虑分批导入
- 增加MariaDB的内存配置
- 关闭其他应用程序

## 📊 导入后检查

### 数据完整性检查
```sql
-- 检查主要表的记录数
SELECT 
    TABLE_NAME,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'xscj'
ORDER BY TABLE_ROWS DESC
LIMIT 10;

-- 检查字符编码
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'xscj';
```

### 性能优化
```sql
-- 重建索引统计信息
ANALYZE TABLE xmMeters;

-- 优化表
OPTIMIZE TABLE xmMeters;
```

## 🔧 故障排除

### 如果导入失败
1. **检查错误日志**: 查看MariaDB错误日志
2. **分段导入**: 将大文件分割成小文件
3. **跳过错误**: 使用 `--force` 参数继续导入
4. **检查权限**: 确保用户有足够权限

### 分段导入方法
```bash
# 如果文件太大，可以分割
split -l 100000 export_202511111009_final.sql part_

# 然后逐个导入
mysql -u root -p xscj < part_aa
mysql -u root -p xscj < part_ab
# ... 继续
```

## 📞 技术支持

如果遇到问题，请检查：
1. MariaDB版本兼容性
2. 系统资源（内存、磁盘空间）
3. 网络连接稳定性
4. 用户权限设置

## 🎯 成功标志

导入成功后，您应该能够：
- 看到 `xscj` 数据库
- 查询到所有表和数据
- 正常执行SQL查询
- 数据显示正确的中文字符

---

**祝您导入成功！** 🎉
