@echo off
chcp 65001 >nul
echo ================================================================================
echo SQL Server to MariaDB Converter
echo ================================================================================
echo.

if "%~1"=="" (
    echo 使用方法:
    echo   convert_sql.bat input.sql [output.sql]
    echo.
    echo 示例:
    echo   convert_sql.bat backup.sql
    echo   convert_sql.bat backup.sql converted.sql
    echo.
    echo 或者直接运行GUI版本:
    echo   convert_sql.bat gui
    echo.
    pause
    exit /b 1
)

if /i "%~1"=="gui" (
    echo 启动GUI版本...
    python sql_converter_gui.py
    exit /b 0
)

if not exist "%~1" (
    echo 错误: 输入文件不存在: %~1
    pause
    exit /b 1
)

echo 输入文件: %~1
if "%~2"=="" (
    echo 输出文件: 自动生成
    python sqlserver_to_mariadb_converter.py "%~1"
) else (
    echo 输出文件: %~2
    python sqlserver_to_mariadb_converter.py "%~1" -o "%~2"
)

echo.
echo 转换完成!
pause
