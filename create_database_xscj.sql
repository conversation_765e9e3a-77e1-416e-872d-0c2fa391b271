-- 创建xscj数据库的兼容性SQL语句
-- 适用于MariaDB 10.0+

-- 方案1: 标准的utf8mb4创建语句
CREATE DATABASE IF NOT EXISTS `xscj` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 如果上面的语句报错，请尝试方案2
-- 方案2: 使用utf8字符集
-- CREATE DATABASE IF NOT EXISTS `xscj` 
-- DEFAULT CHARACTER SET utf8 
-- COLLATE utf8_unicode_ci;

-- 如果方案2还是报错，请尝试方案3
-- 方案3: 最简单的创建语句
-- CREATE DATABASE IF NOT EXISTS `xscj`;

-- 使用数据库
USE `xscj`;

-- 显示数据库信息
SHOW CREATE DATABASE `xscj`;
