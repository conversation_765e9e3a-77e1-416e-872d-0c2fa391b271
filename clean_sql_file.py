#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理SQL文件中的问题语句
"""

import re
import os

def clean_sql_file(input_file, output_file=None):
    """清理SQL文件中的兼容性问题"""
    
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_cleaned.sql"
    
    print(f"清理文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        with open(input_file, 'r', encoding='gbk') as f:
            content = f.read()
        print("使用GBK编码读取文件")
    
    print("开始清理SQL文件...")
    
    # 清理问题语句
    fixes = []
    
    # 1. 移除或替换有问题的CREATE DATABASE语句
    create_db_patterns = [
        r'CREATE DATABASE \/\*!32312 IF NOT EXISTS\*\/`([^`]+)` \/\*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4[^*]*\*\/ \/\*!80016[^*]*\*\/',
        r'CREATE DATABASE \/\*!32312 IF NOT EXISTS\*\/`([^`]+)` \/\*!40100[^*]*\*\/',
        r'CREATE DATABASE \/\*[^*]*\*\/ `([^`]+)` \/\*[^*]*\*\/',
    ]
    
    for pattern in create_db_patterns:
        matches = re.findall(pattern, content)
        if matches:
            db_name = matches[0] if matches else 'database'
            replacement = f'CREATE DATABASE IF NOT EXISTS `{db_name}` DEFAULT CHARACTER SET utf8;'
            content = re.sub(pattern, replacement, content)
            fixes.append(f"替换CREATE DATABASE语句为兼容版本")
            break
    
    # 2. 移除版本特定的注释
    version_comments = [
        r'\/\*!80016[^*]*\*\/',
        r'\/\*!80013[^*]*\*\/',
        r'\/\*!80011[^*]*\*\/',
        r'\/\*!50717[^*]*\*\/',
        r'\/\*!50716[^*]*\*\/',
        r'\/\*!40101[^*]*\*\/',
        r'\/\*!40100[^*]*\*\/',
        r'\/\*!32312[^*]*\*\/',
    ]
    
    for pattern in version_comments:
        if re.search(pattern, content):
            content = re.sub(pattern, '', content)
            fixes.append("移除版本特定注释")
    
    # 3. 替换utf8mb4为utf8（如果需要）
    if 'utf8mb4' in content:
        # 只在CREATE语句中替换，避免影响数据
        content = re.sub(r'DEFAULT CHARACTER SET utf8mb4', 'DEFAULT CHARACTER SET utf8', content)
        content = re.sub(r'COLLATE utf8mb4_[a-zA-Z_]+', 'COLLATE utf8_general_ci', content)
        fixes.append("utf8mb4 → utf8")
    
    # 4. 清理多余的空白
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # 多个空行变为两个
    content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)  # 移除行尾空格
    
    # 写入清理后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("\n清理完成!")
    print("=" * 50)
    if fixes:
        print("应用的修复:")
        for fix in fixes:
            print(f"  ✓ {fix}")
    else:
        print("  - 未发现需要清理的问题")
    
    print("=" * 50)
    print(f"清理后的文件: {output_file}")
    
    return output_file

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法: python clean_sql_file.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        result = clean_sql_file(input_file, output_file)
        print(f"\n建议导入顺序:")
        print(f"1. 先运行: SOURCE create_database_xscj.sql;")
        print(f"2. 再运行: SOURCE {result};")
        
    except Exception as e:
        print(f"清理失败: {str(e)}")
        import traceback
        traceback.print_exc()
