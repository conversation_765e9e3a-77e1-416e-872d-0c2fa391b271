#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大文件SQL Server to MariaDB转换器
专门处理包含大量二进制数据的大型SQL文件
"""

import re
import os
import sys
from sqlserver_to_mariadb_converter import SQLServerToMariaDBConverter


class LargeFileSQLConverter(SQLServerToMariaDBConverter):
    def __init__(self, chunk_size=1024*1024):  # 1MB chunks
        super().__init__()
        self.chunk_size = chunk_size
    
    def convert_large_file(self, input_file: str, output_file: str = None) -> str:
        """转换大型SQL文件，使用流式处理"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 确定输出文件名
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_mariadb.sql"
        
        file_size = os.path.getsize(input_file)
        print(f"处理大文件: {input_file}")
        print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
        
        # 使用流式处理
        try:
            with open(input_file, 'r', encoding='utf-8') as infile:
                content = infile.read()
        except UnicodeDecodeError:
            with open(input_file, 'r', encoding='gbk') as infile:
                content = infile.read()
            print("使用GBK编码读取文件")
        
        print("开始转换...")
        
        # 对于大文件，我们需要更谨慎的处理
        # 先处理简单的替换
        print("1. 处理GO语句...")
        content = self.handle_go_statements(content)
        
        print("2. 处理USE语句...")
        content = self.handle_use_database(content)
        
        print("3. 移除SQL Server特有语句...")
        content = self.remove_sqlserver_specific(content)
        
        print("4. 处理IDENTITY列...")
        content = self.handle_identity_columns(content)
        
        print("5. 处理函数转换...")
        content = self.convert_functions(content)
        
        print("6. 处理数据类型转换...")
        content = self.convert_data_types_safe(content)
        
        print("7. 处理标识符转换...")
        content = self.convert_identifiers_safe(content)
        
        print("8. 处理约束...")
        content = self.handle_constraints(content)
        
        print("9. 清理MariaDB语法...")
        content = self.clean_mariadb_syntax(content)
        
        print("10. 清理空白字符...")
        content = self.clean_whitespace(content)
        
        # 写入输出文件
        print("写入输出文件...")
        with open(output_file, 'w', encoding='utf-8') as outfile:
            outfile.write(content)
        
        output_size = os.path.getsize(output_file)
        print(f"转换完成!")
        print(f"输出文件: {output_file}")
        print(f"输出大小: {output_size:,} 字节 ({output_size/1024/1024:.2f} MB)")
        
        return output_file
    
    def convert_identifiers_safe(self, sql: str) -> str:
        """安全的标识符转换，避免处理二进制数据"""
        print("   处理标识符转换（安全模式）...")
        
        # 分行处理，避免在二进制数据中进行替换
        lines = sql.split('\n')
        converted_lines = []
        
        for i, line in enumerate(lines):
            if i % 10000 == 0:
                print(f"   处理第 {i:,} 行...")
            
            # 跳过包含大量十六进制数据的行
            if '0x' in line and len(line) > 1000:
                converted_lines.append(line)
                continue
            
            # 只在SQL语句行进行标识符转换
            if any(keyword in line.upper() for keyword in ['INSERT', 'CREATE', 'ALTER', 'SELECT', 'UPDATE', 'DELETE']):
                line = re.sub(r'\[([^\]]+)\]', r'`\1`', line)
            
            converted_lines.append(line)
        
        return '\n'.join(converted_lines)
    
    def convert_data_types_safe(self, sql: str) -> str:
        """安全的数据类型转换"""
        print("   处理数据类型转换（安全模式）...")
        
        # 只在CREATE TABLE语句中进行数据类型转换
        lines = sql.split('\n')
        converted_lines = []
        in_create_table = False
        
        for line in lines:
            original_line = line
            line_upper = line.upper().strip()
            
            # 检测CREATE TABLE语句
            if line_upper.startswith('CREATE TABLE'):
                in_create_table = True
            elif in_create_table and (line_upper.startswith(')') or line_upper == ''):
                in_create_table = False
            
            # 只在CREATE TABLE内部进行数据类型转换
            if in_create_table and not ('0x' in line and len(line) > 100):
                for pattern, replacement in self.data_type_mappings.items():
                    line = re.sub(pattern, replacement, line, flags=re.IGNORECASE)
            
            converted_lines.append(line)
        
        return '\n'.join(converted_lines)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python large_file_converter.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        converter = LargeFileSQLConverter()
        result = converter.convert_large_file(input_file, output_file)
        print(f"\n转换成功完成!")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {result}")
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
