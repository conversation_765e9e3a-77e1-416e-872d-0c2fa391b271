#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复SQL Server三段式表名为MariaDB兼容格式
将 database.schema.table 格式转换为 database.table 或 table
"""

import re
import os
import sys


def fix_table_names(input_file, output_file=None, target_database=None):
    """修复表名格式"""
    
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 - {input_file}")
        return False
    
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_fixed_tables.sql"
    
    file_size = os.path.getsize(input_file)
    print(f"修复文件: {input_file}")
    print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    print(f"输出文件: {output_file}")
    
    try:
        # 读取文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        with open(input_file, 'r', encoding='gbk') as f:
            content = f.read()
        print("使用GBK编码读取文件")
    
    print("开始修复表名...")
    
    # 统计修复信息
    fixes_applied = []
    
    # 1. 查找所有的三段式表名 (database.schema.table)
    three_part_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\.(dbo|[a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)\b'
    
    matches = re.findall(three_part_pattern, content)
    if matches:
        print(f"发现 {len(matches)} 个三段式表名:")
        unique_tables = set(matches)
        for db, schema, table in unique_tables:
            print(f"  {db}.{schema}.{table}")
        
        # 替换三段式表名
        if target_database:
            # 替换为指定数据库的表名
            replacement = rf'{target_database}.`\3`'
            content = re.sub(three_part_pattern, replacement, content)
            fixes_applied.append(f"三段式表名 → {target_database}.table")
        else:
            # 只保留表名
            replacement = r'`\3`'
            content = re.sub(three_part_pattern, replacement, content)
            fixes_applied.append("三段式表名 → table")
    
    # 2. 处理可能的两段式表名问题 (database.table)
    two_part_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\.(image_license|xmMeters|[a-zA-Z_][a-zA-Z0-9_]*)\b'
    
    # 检查是否有两段式表名需要处理
    two_part_matches = re.findall(two_part_pattern, content)
    if two_part_matches:
        print(f"发现 {len(two_part_matches)} 个两段式表名:")
        unique_two_part = set(two_part_matches)
        for db, table in unique_two_part:
            print(f"  {db}.{table}")
        
        if target_database:
            # 替换为指定数据库
            replacement = rf'{target_database}.`\2`'
            content = re.sub(two_part_pattern, replacement, content)
            fixes_applied.append(f"两段式表名 → {target_database}.table")
        else:
            # 只保留表名
            replacement = r'`\2`'
            content = re.sub(two_part_pattern, replacement, content)
            fixes_applied.append("两段式表名 → table")
    
    # 3. 确保所有表名都用反引号包围
    table_name_pattern = r'\b(INSERT\s+INTO|UPDATE|FROM|JOIN)\s+([a-zA-Z_][a-zA-Z0-9_]*)\b'
    content = re.sub(table_name_pattern, r'\1 `\2`', content, flags=re.IGNORECASE)
    
    # 4. 修复可能的重复反引号问题
    content = re.sub(r'`{2,}', '`', content)  # 多个反引号变为单个
    content = re.sub(r'``([^`]+)``', r'`\1`', content)  # 双重反引号变为单个
    
    # 5. 添加USE语句（如果指定了目标数据库）
    if target_database:
        use_statement = f"USE `{target_database}`;\n\n"
        content = use_statement + content
        fixes_applied.append(f"添加 USE {target_database} 语句")
    
    # 写入修复后的文件
    print("写入修复后的文件...")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    output_size = os.path.getsize(output_file)
    
    # 显示修复结果
    print("\n修复完成!")
    print("=" * 60)
    print(f"输出文件: {output_file}")
    print(f"输出大小: {output_size:,} 字节 ({output_size/1024/1024:.2f} MB)")
    
    if fixes_applied:
        print("\n应用的修复:")
        for fix in fixes_applied:
            print(f"  ✓ {fix}")
    else:
        print("\n  - 未发现需要修复的表名问题")
    
    print("=" * 60)
    
    return True


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python fix_table_names.py <input_file> [output_file] [target_database]")
        print("")
        print("参数说明:")
        print("  input_file      - 输入的SQL文件")
        print("  output_file     - 输出文件名（可选）")
        print("  target_database - 目标数据库名（可选）")
        print("")
        print("示例:")
        print("  python fix_table_names.py export_202511111009_mariadb.sql")
        print("  python fix_table_names.py export_202511111009_mariadb.sql fixed.sql")
        print("  python fix_table_names.py export_202511111009_mariadb.sql fixed.sql xscj")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    target_database = sys.argv[3] if len(sys.argv) > 3 else None
    
    try:
        success = fix_table_names(input_file, output_file, target_database)
        if success:
            print("\n建议:")
            if target_database:
                print(f"1. 确保数据库 '{target_database}' 已经存在")
                print(f"2. 导入命令: mysql -u username -p {target_database} < {output_file or input_file.replace('.sql', '_fixed_tables.sql')}")
            else:
                print("1. 先创建目标数据库")
                print("2. 使用 USE database_name; 切换到目标数据库")
                print(f"3. 导入命令: mysql -u username -p < {output_file or input_file.replace('.sql', '_fixed_tables.sql')}")
        
    except Exception as e:
        print(f"修复失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
