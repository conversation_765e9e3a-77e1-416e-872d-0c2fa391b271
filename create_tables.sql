-- 自动生成的表结构
-- 请在导入数据前先执行这些CREATE TABLE语句

CREATE TABLE `xmMeters` (
    `fdno` VARCHAR(100),
    `pcno` VARCHAR(100),
    `jfDate` VARCHAR(100),
    `flno` VARCHAR(100),
    `wyno` VARCHAR(100),
    `yhno` VARCHAR(100),
    `xmno` VARCHAR(100),
    `syds` DECIMAL(18,2),
    `byds` DECIMAL(18,2),
    `bb` DECIMAL(18,2),
    `sjyl` DECIMAL(18,2),
    `dj` DECIMAL(18,2),
    `sjje` DECIMAL(18,2),
    `shsl` DECIMAL(18,2),
    `shje` DECIMAL(18,2),
    `je` DECIMAL(18,2),
    `fromDate` VARCHAR(100),
    `toDate` VARCHAR(100),
    `jsDate` VARCHAR(100),
    `bz` VARCHAR(100),
    `opname` VARCHAR(100),
    `sh` TINYINT,
    `shname` VARCHAR(255),
    `cbdj` DECIMAL(18,2),
    `fj1` DECIMAL(18,2),
    `fj2` DECIMAL(18,2),
    `fj3` DECIMAL(18,2),
    `psjyl` DECIMAL(18,2),
    `pdj` DECIMAL(18,2),
    `pje` DECIMAL(18,2),
    `fsjyl` DECIMAL(18,2),
    `fdj` DECIMAL(18,2),
    `fje` DECIMAL(18,2),
    `gsjyl` DECIMAL(18,2),
    `gdj` DECIMAL(18,2),
    `gje` DECIMAL(18,2),
    `xb` DECIMAL(18,2),
    `maxds` DECIMAL(18,2),
    `meterName` VARCHAR(255),
    `minds` DECIMAL(18,2),
    `meterName1` VARCHAR(255),
    `devName` VARCHAR(255),
    `parMeter` VARCHAR(255),
    `meterAttr` VARCHAR(255),
    `addXm` VARCHAR(255),
    `decXm` VARCHAR(255),
    `Gtxmname` VARCHAR(255),
    `meterIndex` DECIMAL(18,2),
    `zfsjyl` DECIMAL(18,2),
    `zfdj` DECIMAL(18,2),
    `zfje` DECIMAL(18,2),
    `UseElectricity` VARCHAR(255),
    `RemainingAmount` VARCHAR(255),
    `MeterStatus` VARCHAR(255),
    `MeterState` VARCHAR(255),
    `MeterReadTime` VARCHAR(255),
    `MeterCreateDate` VARCHAR(255),
    `tzdType` VARCHAR(255),
    `batch` VARCHAR(255),
    `fsjyl_t` DECIMAL(18,2),
    `zfsjyl_t` DECIMAL(18,2),
    `psjyl_t` DECIMAL(18,2),
    `gsjyl_t` DECIMAL(18,2),
    `fpgSum` DECIMAL(18,2),
    `meterTime` VARCHAR(255)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

