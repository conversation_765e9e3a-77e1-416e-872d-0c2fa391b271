#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的表结构
"""

import re
import os

def analyze_optimized_tables():
    """分析优化后的表结构"""
    
    file_path = "create_all_tables_optimized.sql"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("=" * 60)
    print("优化后的表结构分析")
    print("=" * 60)
    
    # 统计信息
    stats = {
        "CREATE TABLE语句": len(re.findall(r'CREATE TABLE', content)),
        "TEXT字段": content.count(' TEXT'),
        "VARCHAR字段": len(re.findall(r'VARCHAR\(\d+\)', content)),
        "LONGTEXT字段": content.count(' LONGTEXT'),
        "ROW_FORMAT=DYNAMIC": content.count('ROW_FORMAT=DYNAMIC'),
    }
    
    print("统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 分析VARCHAR大小分布
    varchar_sizes = re.findall(r'VARCHAR\((\d+)\)', content)
    if varchar_sizes:
        sizes = [int(size) for size in varchar_sizes]
        print(f"\nVARCHAR大小分布:")
        print(f"  最小: {min(sizes)}")
        print(f"  最大: {max(sizes)}")
        print(f"  平均: {sum(sizes)/len(sizes):.1f}")
        
        # 统计不同大小范围
        size_ranges = {
            "1-50": len([s for s in sizes if 1 <= s <= 50]),
            "51-100": len([s for s in sizes if 51 <= s <= 100]),
            "101-255": len([s for s in sizes if 101 <= s <= 255]),
            "256+": len([s for s in sizes if s > 255]),
        }
        
        print("  大小范围分布:")
        for range_name, count in size_ranges.items():
            print(f"    {range_name}: {count}")
    
    # 检查可能的问题表
    print(f"\n检查潜在问题:")
    
    # 查找仍然可能有行大小问题的表
    tables = re.findall(r'CREATE TABLE `([^`]+)` \(([^)]+)\);', content, re.DOTALL)
    
    problem_tables = []
    for table_name, table_def in tables:
        # 估算行大小
        varchar_matches = re.findall(r'VARCHAR\((\d+)\)', table_def)
        text_count = table_def.count(' TEXT')
        
        estimated_size = sum(int(size) for size in varchar_matches) + text_count * 100  # TEXT估算100字节
        
        if estimated_size > 50000:  # 如果估算大小超过50KB
            problem_tables.append((table_name, estimated_size, len(varchar_matches) + text_count))
    
    if problem_tables:
        print(f"  可能仍有问题的表: {len(problem_tables)}")
        for table_name, size, field_count in problem_tables[:5]:  # 显示前5个
            print(f"    {table_name}: 估算{size}字节, {field_count}字段")
        if len(problem_tables) > 5:
            print(f"    ... 还有 {len(problem_tables) - 5} 个")
    else:
        print("  ✓ 未发现明显的行大小问题")
    
    # 文件大小
    file_size = os.path.getsize(file_path)
    print(f"\n文件信息:")
    print(f"  大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = len(f.readlines())
    print(f"  行数: {lines:,}")
    
    print("=" * 60)
    print("优化建议:")
    print("1. 使用 ROW_FORMAT=DYNAMIC 以支持更大的行")
    print("2. TEXT字段已优化，减少了行大小")
    print("3. 如果仍有问题，可以进一步将VARCHAR改为TEXT")
    print("=" * 60)

if __name__ == '__main__':
    analyze_optimized_tables()
