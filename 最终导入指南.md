# 最终导入指南 - 简化版本

## 🎉 重新转换完成！

按照您的要求，已经重新转换了SQL文件，现在使用简化的表名格式。

### 📁 文件信息
- **原始文件**: `export_202511111009.sql` (288.95 MB)
- **转换文件**: `export_202511111009_clean.sql` (287.74 MB)
- **表名格式**: 只保留最终表名，如 `xmMeters`

### ✅ 转换详情
- **三段式表名**: 114,757 处 `jtpmGp15.dbo.xmMeters` → `xmMeters`
- **唯一表数量**: 963 个不同的表
- **文件大小**: 减少了约 1.2 MB
- **无数据库指定**: 可以导入到任何数据库

### 📋 转换示例
```sql
-- 转换前
INSERT INTO jtpmGp15.dbo.xmMeters (fdno,pcno,jfDate,...) VALUES

-- 转换后  
INSERT INTO `xmMeters` (fdno,pcno,jfDate,...) VALUES
```

## 🚀 导入步骤

### 步骤1: 创建目标数据库
```sql
-- 在MariaDB中执行（根据需要选择字符集）
CREATE DATABASE xscj DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 如果不支持utf8mb4，使用utf8
-- CREATE DATABASE xscj DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;

-- 或者最简单的方式
-- CREATE DATABASE xscj;
```

### 步骤2: 导入数据
```bash
# 方法1: 命令行导入
mysql -u root -p xscj < export_202511111009_clean.sql

# 方法2: MariaDB客户端
mariadb -u root -p xscj < export_202511111009_clean.sql
```

### 步骤3: 在MariaDB客户端中导入
```sql
-- 连接到MariaDB
mysql -u root -p

-- 使用数据库
USE xscj;

-- 导入文件
SOURCE export_202511111009_clean.sql;
```

## 📊 导入验证

### 检查导入结果
```sql
-- 查看所有表
SHOW TABLES;

-- 检查主要表的数据
SELECT COUNT(*) FROM xmMeters;

-- 查看表结构
DESCRIBE xmMeters;

-- 检查字符编码
SHOW CREATE TABLE xmMeters;
```

### 预期结果
- 应该看到 963 个表
- `xmMeters` 表应该包含大量记录
- 中文数据应该正确显示

## ⚙️ 配置建议

### MariaDB配置优化
```ini
# 在 my.cnf 或 my.ini 中添加
[mysql]
max_allowed_packet = 1024M
default-character-set = utf8mb4

[mysqld]
max_allowed_packet = 1024M
innodb_buffer_pool_size = 1G
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
wait_timeout = 28800
interactive_timeout = 28800
```

### 导入前设置
```sql
-- 设置字符集
SET NAMES utf8mb4;

-- 增加包大小限制
SET GLOBAL max_allowed_packet = 1073741824;

-- 增加超时时间
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 字符集问题
```sql
-- 如果遇到字符集错误
SET NAMES utf8;
-- 或重新创建数据库
DROP DATABASE IF EXISTS xscj;
CREATE DATABASE xscj DEFAULT CHARACTER SET utf8;
```

#### 2. 包大小限制
```bash
# 启动MariaDB时增加参数
mysqld --max_allowed_packet=1G
```

#### 3. 内存不足
- 关闭其他应用程序
- 增加系统虚拟内存
- 考虑分批导入

#### 4. 导入超时
```sql
-- 在导入前执行
SET SESSION wait_timeout = 86400;  -- 24小时
SET SESSION interactive_timeout = 86400;
```

### 分批导入（如果需要）
```bash
# 如果文件太大，可以分割
split -l 50000 export_202511111009_clean.sql part_

# 逐个导入
mysql -u root -p xscj < part_aa
mysql -u root -p xscj < part_ab
# 继续...
```

## 📈 性能优化

### 导入后优化
```sql
-- 分析表统计信息
ANALYZE TABLE xmMeters;

-- 优化表
OPTIMIZE TABLE xmMeters;

-- 检查索引使用情况
SHOW INDEX FROM xmMeters;
```

### 查询优化
```sql
-- 如果查询慢，可以添加索引
-- 例如：
ALTER TABLE xmMeters ADD INDEX idx_fdno (fdno);
ALTER TABLE xmMeters ADD INDEX idx_jfdate (jfDate);
```

## 🎯 成功标志

导入成功后，您应该能够：
- ✅ 看到 963 个表
- ✅ 查询 `xmMeters` 表有数据
- ✅ 中文字符显示正确
- ✅ 所有表名都是简化格式（如 `xmMeters`）

## 📞 技术支持

如果遇到问题：
1. 检查 MariaDB 错误日志
2. 确认磁盘空间充足（至少 1GB 可用）
3. 验证用户权限
4. 检查 MariaDB 版本兼容性

---

**现在您的SQL文件已经完全兼容MariaDB，可以导入到任何数据库中！** 🎉

### 快速导入命令
```bash
# 一键导入（替换 your_database_name）
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS your_database_name;"
mysql -u root -p your_database_name < export_202511111009_clean.sql
```
