#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MariaDB字符集兼容性问题
将utf8mb4替换为兼容的字符集
"""

import re
import os
import sys


def fix_charset_issues(input_file, output_file=None):
    """修复字符集兼容性问题"""
    
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 - {input_file}")
        return False
    
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_fixed.sql"
    
    print(f"修复文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    try:
        # 读取文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        with open(input_file, 'r', encoding='gbk') as f:
            content = f.read()
        print("使用GBK编码读取文件")
    
    print("开始修复字符集问题...")
    
    # 修复字符集相关问题
    fixes_applied = []
    
    # 1. 替换 utf8mb4 为 utf8
    if 'utf8mb4' in content:
        content = content.replace('utf8mb4', 'utf8')
        fixes_applied.append("utf8mb4 → utf8")
    
    # 2. 移除 DEFAULT ENCRYPTION 子句（MariaDB旧版本不支持）
    encryption_pattern = r'\/\*!80016 DEFAULT ENCRYPTION=\'[^\']*\' \*\/'
    if re.search(encryption_pattern, content):
        content = re.sub(encryption_pattern, '', content)
        fixes_applied.append("移除 DEFAULT ENCRYPTION 子句")
    
    # 3. 处理版本特定的注释
    # 替换高版本的MySQL注释为更兼容的版本
    version_fixes = [
        (r'\/\*!80016([^*]|\*(?!\/))*\*\/', ''),  # 移除8.0.16特定注释
        (r'\/\*!80013([^*]|\*(?!\/))*\*\/', ''),  # 移除8.0.13特定注释
        (r'\/\*!80011([^*]|\*(?!\/))*\*\/', ''),  # 移除8.0.11特定注释
        (r'\/\*!50717([^*]|\*(?!\/))*\*\/', ''),  # 移除5.7.17特定注释
        (r'\/\*!50716([^*]|\*(?!\/))*\*\/', ''),  # 移除5.7.16特定注释
        (r'\/\*!50715([^*]|\*(?!\/))*\*\/', ''),  # 移除5.7.15特定注释
    ]
    
    for pattern, replacement in version_fixes:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            fixes_applied.append(f"移除高版本特定注释")
    
    # 4. 修复CREATE DATABASE语句
    # 简化CREATE DATABASE语句，使其更兼容
    create_db_pattern = r'CREATE DATABASE \/\*!32312 IF NOT EXISTS\*\/`([^`]+)` \/\*!40100 DEFAULT CHARACTER SET utf8 COLLATE utf8_[^*]* \*\/'
    if re.search(create_db_pattern, content):
        content = re.sub(create_db_pattern, r'CREATE DATABASE IF NOT EXISTS `\1` DEFAULT CHARACTER SET utf8', content)
        fixes_applied.append("简化 CREATE DATABASE 语句")
    
    # 5. 处理其他可能的兼容性问题
    compatibility_fixes = [
        # 移除ROW_FORMAT=DYNAMIC（某些版本不支持）
        (r'ROW_FORMAT=DYNAMIC', ''),
        # 简化ENGINE注释
        (r'\/\*!40101 ENGINE=([^*]|\*(?!\/))*\*\/', ''),
        # 移除KEY_BLOCK_SIZE（如果存在）
        (r'KEY_BLOCK_SIZE=\d+', ''),
    ]
    
    for pattern, replacement in compatibility_fixes:
        if re.search(pattern, content, re.IGNORECASE):
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            fixes_applied.append(f"兼容性修复: {pattern}")
    
    # 6. 清理多余的空格和空行
    content = re.sub(r'\s+', ' ', content)  # 多个空格变为单个
    content = re.sub(r'\s*;\s*', ';\n', content)  # 分号后换行
    content = re.sub(r'\n\s*\n', '\n', content)  # 移除多余空行
    
    # 写入修复后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 显示修复结果
    print("\n修复完成!")
    print("=" * 50)
    if fixes_applied:
        print("应用的修复:")
        for fix in fixes_applied:
            print(f"  ✓ {fix}")
    else:
        print("  - 未发现需要修复的问题")
    
    print("=" * 50)
    print(f"输出文件: {output_file}")
    
    return True


def create_compatible_create_database(database_name):
    """创建兼容的CREATE DATABASE语句"""
    compatible_statements = [
        f"-- 兼容的CREATE DATABASE语句",
        f"-- 适用于MariaDB 5.5+",
        f"CREATE DATABASE IF NOT EXISTS `{database_name}` DEFAULT CHARACTER SET utf8;",
        f"",
        f"-- 如果上面的语句仍然有问题，请尝试:",
        f"-- CREATE DATABASE IF NOT EXISTS `{database_name}`;",
        f"",
        f"USE `{database_name}`;",
        f""
    ]
    
    return '\n'.join(compatible_statements)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python fix_charset_compatibility.py <input_file> [output_file]")
        print("")
        print("示例:")
        print("  python fix_charset_compatibility.py export_202511111009_mariadb.sql")
        print("  python fix_charset_compatibility.py export_202511111009_mariadb.sql fixed_export.sql")
        print("")
        print("也可以直接生成兼容的CREATE DATABASE语句:")
        print("  python fix_charset_compatibility.py --create-db database_name")
        return
    
    if sys.argv[1] == '--create-db':
        if len(sys.argv) < 3:
            print("请提供数据库名称")
            return
        
        db_name = sys.argv[2]
        compatible_sql = create_compatible_create_database(db_name)
        
        output_file = f"create_{db_name}_compatible.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(compatible_sql)
        
        print(f"兼容的CREATE DATABASE语句已保存到: {output_file}")
        print("\n内容:")
        print(compatible_sql)
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        success = fix_charset_issues(input_file, output_file)
        if success:
            print("\n建议:")
            print("1. 检查您的MariaDB版本: SELECT VERSION();")
            print("2. 如果仍有问题，请尝试使用更简单的字符集:")
            print("   CREATE DATABASE database_name DEFAULT CHARACTER SET latin1;")
            print("3. 或者升级MariaDB到支持utf8mb4的版本")
        
    except Exception as e:
        print(f"修复失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
