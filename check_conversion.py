#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查转换结果
"""

import os

def check_conversion_result():
    """检查转换结果"""
    input_file = "export_202511111009.sql"
    output_file = "export_202511111009_mariadb.sql"
    
    if not os.path.exists(output_file):
        print("转换文件不存在!")
        return
    
    # 检查文件大小
    input_size = os.path.getsize(input_file)
    output_size = os.path.getsize(output_file)
    
    print("=" * 60)
    print("转换结果检查")
    print("=" * 60)
    print(f"原文件: {input_file}")
    print(f"原文件大小: {input_size:,} 字节 ({input_size/1024/1024:.2f} MB)")
    print(f"转换文件: {output_file}")
    print(f"转换文件大小: {output_size:,} 字节 ({output_size/1024/1024:.2f} MB)")
    print(f"大小变化: {output_size - input_size:,} 字节")
    
    # 读取前几行进行比较
    print("\n" + "=" * 60)
    print("转换前后对比 (前5行)")
    print("=" * 60)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            original_lines = [f.readline().strip() for _ in range(5)]
    except:
        with open(input_file, 'r', encoding='gbk') as f:
            original_lines = [f.readline().strip() for _ in range(5)]
    
    with open(output_file, 'r', encoding='utf-8') as f:
        converted_lines = [f.readline().strip() for _ in range(5)]
    
    for i, (orig, conv) in enumerate(zip(original_lines, converted_lines), 1):
        print(f"\n第{i}行:")
        print(f"原始: {orig[:100]}{'...' if len(orig) > 100 else ''}")
        print(f"转换: {conv[:100]}{'...' if len(conv) > 100 else ''}")
        if orig != conv:
            print("  ✓ 已转换")
        else:
            print("  - 无变化")
    
    # 检查转换的关键词
    print("\n" + "=" * 60)
    print("转换检查")
    print("=" * 60)
    
    with open(output_file, 'r', encoding='utf-8') as f:
        content_sample = f.read(10000)  # 读取前10KB检查
    
    conversions = {
        "方括号标识符": "`" in content_sample and "[" not in content_sample[:1000],
        "INSERT语句": "INSERT INTO" in content_sample,
        "二进制数据": "0x" in content_sample,
        "VALUES语句": "VALUES" in content_sample,
    }
    
    for check, result in conversions.items():
        status = "✓" if result else "✗"
        print(f"{status} {check}")
    
    print("\n" + "=" * 60)
    print("转换完成! 文件可以导入MariaDB了。")
    print("建议:")
    print("1. 在导入前先备份MariaDB数据库")
    print("2. 在测试环境中先验证导入")
    print("3. 检查字符编码是否正确")
    print("=" * 60)

if __name__ == '__main__':
    check_conversion_result()
