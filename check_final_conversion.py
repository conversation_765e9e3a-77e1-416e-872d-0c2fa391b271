#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查最终转换结果
"""

import os
import re

def check_final_conversion():
    """检查最终转换结果"""
    input_file = "export_202511111009_clean.sql"
    
    if not os.path.exists(input_file):
        print("转换文件不存在!")
        return
    
    # 检查文件大小
    file_size = os.path.getsize(input_file)
    
    print("=" * 70)
    print("最终转换结果检查")
    print("=" * 70)
    print(f"文件: {input_file}")
    print(f"大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    
    # 读取文件进行分析
    print("\n正在分析文件内容...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        # 读取前1000行进行分析
        sample_lines = []
        for i, line in enumerate(f):
            if i >= 1000:
                break
            sample_lines.append(line.strip())
    
    sample_content = '\n'.join(sample_lines)
    
    print("\n" + "=" * 70)
    print("转换质量检查")
    print("=" * 70)
    
    # 检查转换质量
    checks = {
        "简化表名": len(re.findall(r'INSERT INTO `[^`]+`', sample_content)),
        "反引号使用": sample_content.count('`'),
        "三段式残留": len(re.findall(r'\w+\.\w+\.\w+', sample_content)),
        "INSERT语句": sample_content.count('INSERT INTO'),
        "VALUES语句": sample_content.count('VALUES'),
        "二进制数据": sample_content.count('0x'),
    }
    
    for check, count in checks.items():
        status = "✓" if count > 0 else "✗"
        if check == "三段式残留":
            status = "✓" if count == 0 else f"⚠ ({count})"
        print(f"{status} {check}: {count}")
    
    # 检查表名示例
    print(f"\n" + "=" * 70)
    print("表名转换示例")
    print("=" * 70)
    
    table_names = re.findall(r'INSERT INTO `([^`]+)`', sample_content)
    unique_tables = list(set(table_names))[:10]
    
    if unique_tables:
        print("发现的表名:")
        for table in unique_tables:
            print(f"  • {table}")
        if len(set(table_names)) > 10:
            print(f"  ... 还有 {len(set(table_names)) - 10} 个表")
    else:
        print("未发现表名（可能需要检查更多内容）")
    
    # 检查数据完整性
    print(f"\n" + "=" * 70)
    print("数据完整性检查")
    print("=" * 70)
    
    # 统计行数
    with open(input_file, 'r', encoding='utf-8') as f:
        total_lines = sum(1 for _ in f)
    
    print(f"总行数: {total_lines:,}")
    
    # 检查文件开头和结尾
    with open(input_file, 'r', encoding='utf-8') as f:
        first_line = f.readline().strip()
        f.seek(0, 2)  # 移到文件末尾
        file_size = f.tell()
        f.seek(max(0, file_size - 1000))  # 读取最后1000字符
        last_content = f.read().strip()
    
    print(f"首行: {first_line[:100]}...")
    print(f"末尾包含: {'VALUES' if 'VALUES' in last_content else '其他内容'}")
    
    print(f"\n" + "=" * 70)
    print("导入建议")
    print("=" * 70)
    print("1. 文件已准备就绪，可以导入MariaDB")
    print("2. 建议的导入命令:")
    print("   CREATE DATABASE your_db_name;")
    print("   USE your_db_name;")
    print(f"   SOURCE {input_file};")
    print("3. 或使用命令行:")
    print(f"   mysql -u root -p your_db_name < {input_file}")
    
    if checks["三段式残留"] > 0:
        print("\n⚠ 注意: 仍有少量三段式表名残留，可能是URL或其他非表名内容")
    
    print("=" * 70)

if __name__ == '__main__':
    check_final_conversion()
