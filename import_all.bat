@echo off
chcp 65001 >nul
echo ================================================================================
echo SQL Server 数据导入到 MariaDB - 完整解决方案
echo ================================================================================
echo.

echo 请确保：
echo 1. MariaDB 服务已启动
echo 2. 您知道 root 密码
echo 3. 以下文件存在：
echo    - create_all_tables_optimized.sql (933个表的优化结构)
echo    - export_202511111009_clean.sql (数据文件)
echo.

set /p confirm="确认开始导入吗？(y/n): "
if /i not "%confirm%"=="y" (
    echo 取消导入
    pause
    exit /b 0
)

echo.
echo ================================================================================
echo 步骤 1: 创建数据库 'rent'
echo ================================================================================

mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS rent DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if %errorlevel% neq 0 (
    echo 错误：创建数据库失败，尝试使用 utf8 字符集...
    mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS rent DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;"
    
    if %errorlevel% neq 0 (
        echo 错误：创建数据库失败！请检查 MariaDB 连接和权限。
        pause
        exit /b 1
    )
)

echo ✓ 数据库创建成功

echo.
echo ================================================================================
echo 步骤 2: 创建表结构
echo ================================================================================

if not exist "create_all_tables_optimized.sql" (
    echo 错误：找不到 create_all_tables_optimized.sql 文件
    pause
    exit /b 1
)

mysql -u root -p rent < create_all_tables_optimized.sql

if %errorlevel% neq 0 (
    echo 错误：创建表结构失败！
    pause
    exit /b 1
)

echo ✓ 表结构创建成功

echo.
echo ================================================================================
echo 步骤 3: 导入数据（这可能需要几分钟...）
echo ================================================================================

if not exist "export_202511111009_clean.sql" (
    echo 错误：找不到 export_202511111009_clean.sql 文件
    pause
    exit /b 1
)

echo 正在导入数据，请耐心等待...
mysql -u root -p rent < export_202511111009_clean.sql

if %errorlevel% neq 0 (
    echo 错误：数据导入失败！
    echo 可能的原因：
    echo 1. 文件太大，需要调整 MariaDB 配置
    echo 2. 字符集问题
    echo 3. 磁盘空间不足
    pause
    exit /b 1
)

echo ✓ 数据导入成功

echo.
echo ================================================================================
echo 步骤 4: 验证导入结果
echo ================================================================================

echo 检查表和数据...
mysql -u root -p rent -e "SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema='rent'; SELECT COUNT(*) as xmMeters_records FROM xmMeters; SHOW TABLES LIKE 'xm%';"

if %errorlevel% neq 0 (
    echo 警告：验证查询失败，但数据可能已成功导入
)

echo.
echo ================================================================================
echo 导入完成！
echo ================================================================================
echo.
echo 成功导入到数据库：rent
echo 共导入：933个表
echo.
echo 您现在可以：
echo 1. 使用 MySQL/MariaDB 客户端连接数据库
echo 2. 执行查询：SELECT * FROM rent.xmMeters LIMIT 10;
echo 3. 检查数据完整性
echo.
echo 如果遇到问题，请检查：
echo - MariaDB 错误日志
echo - 磁盘空间
echo - 字符编码设置
echo.
pause
